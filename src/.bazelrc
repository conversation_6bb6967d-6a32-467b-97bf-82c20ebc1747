# Copyright 2023 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

common --experimental_repo_remote_exec
common --experimental_cc_shared_library

common --registry=https://raw.githubusercontent.com/secretflow/bazel-registry/main
common --registry=https://bcr.bazel.build
common --registry=https://baidu.github.io/babylon/registry

# Required by OpenXLA
build --nocheck_visibility

build --incompatible_new_actions_api=false
build --copt=-fdiagnostics-color=always
build --enable_platform_specific_config

build --cxxopt=-std=c++17
build --host_cxxopt=-std=c++17

build:avx --copt=-mavx
build:avx --host_copt=-mavx
build:avx --copt=-DCHECK_AVX
build:avx --host_copt=-DCHECK_AVX

# default off CUDA build
build --@rules_cuda//cuda:enable=false

# Only on when asked
build:gpu --@rules_cuda//cuda:archs=compute_80:compute_80
build:gpu --@rules_cuda//cuda:enable=true

# Binary safety flags
build --copt=-fPIC
build --copt=-fstack-protector-strong
build:linux --copt=-Wl,-z,noexecstack
build:macos --copt=-Wa,--noexecstack

# OPENSOURCE-CLEANUP REMOVE BEGIN
build --remote_cache=http://bazelcache.inc.alipay.net
# OPENSOURCE-CLEANUP REMOVE END

build --keep_going
test --test_output=errors

build:benchmark --copt -O3
build:benchmark --copt -march=native

# static link runtime libraries on Linux
build:linux --action_env=BAZEL_LINKOPTS=-static-libstdc++:-static-libgcc
build:linux --action_env=BAZEL_LINKLIBS=-l%:libstdc++.a:-l%:libgcc.a

# platform specific config
# Bazel will automatic pick platform config since we have enable_platform_specific_config set
build:macos --copt=-Xclang=-fopenmp
build:macos --copt=-Wno-unused-command-line-argument
build:macos --features=-supports_dynamic_linker
build:macos --macos_minimum_os=13.0
build:macos --host_macos_minimum_os=13.0
build:macos --action_env MACOSX_DEPLOYMENT_TARGET=13.0

build:linux --copt=-fopenmp
build:linux --linkopt=-fopenmp

# startup --output_user_root=/data/shaojian/.cache/bazel/output_base
# common --repository_cache=/data/shaojian/.cache/bazel/repo_cache
# common --disk_cache=/data/shaojian/.cache/bazel/disk_cache
