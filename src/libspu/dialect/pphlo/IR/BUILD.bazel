# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@llvm-project//mlir:tblgen.bzl", "gentbl_cc_library", "gentbl_filegroup", "td_library")
load("//bazel:spu.bzl", "spu_cc_library")

package(
    default_visibility = ["//visibility:public"],
    licenses = ["notice"],
)

td_library(
    name = "dialect_td_files",
    srcs = [
        "attrs.td",
        "base_enums.td",
        "dialect.td",
        "interface.td",
        "ops.td",
        "types.td",
    ],
    deps = [
        "@llvm-project//mlir:BuiltinDialectTdFiles",
        "@llvm-project//mlir:InferTypeOpInterfaceTdFiles",
        "@llvm-project//mlir:OpBaseTdFiles",
        "@llvm-project//mlir:SideEffectInterfacesTdFiles",
    ],
)

gentbl_cc_library(
    name = "base_enums_inc_gen",
    tbl_outs = [
        (
            ["-gen-enum-decls"],
            "base_enums.h.inc",
        ),
        (
            ["-gen-enum-defs"],
            "base_enums.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "base_enums.td",
    deps = [":dialect_td_files"],
)

gentbl_cc_library(
    name = "interface",
    tbl_outs = [
        (
            ["-gen-type-interface-decls"],
            "interface.h.inc",
        ),
        (
            ["-gen-type-interface-defs"],
            "interface.cpp.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "interface.td",
    deps = [":dialect_td_files"],
)

gentbl_cc_library(
    name = "ops_inc_gen",
    tbl_outs = [
        (
            ["-gen-op-decls"],
            "ops.h.inc",
        ),
        (
            ["-gen-op-defs"],
            "ops.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "ops.td",
    deps = [":dialect_td_files"],
)

gentbl_cc_library(
    name = "types_inc_gen",
    tbl_outs = [
        (
            ["-gen-typedef-decls"],
            "types.h.inc",
        ),
        (
            ["-gen-typedef-defs"],
            "types.cc.inc",
        ),
        (
            ["-gen-dialect-decls"],
            "dialect.h.inc",
        ),
        (
            ["-gen-dialect-defs"],
            "dialect.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "types.td",
    deps = [":dialect_td_files"],
)

gentbl_cc_library(
    name = "attrs_inc_gen",
    tbl_outs = [
        (
            ["-gen-attrdef-decls"],
            "attrs.h.inc",
        ),
        (
            ["-gen-attrdef-defs"],
            "attrs.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "ops.td",
    deps = [":dialect_td_files"],
)

gentbl_cc_library(
    name = "canonicalization_pattern_inc_gen",
    tbl_outs = [
        (
            ["-gen-rewriters"],
            "canonicalization_patterns.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "canonicalization_patterns.td",
    deps = [
        ":dialect_td_files",
        "@llvm-project//mlir:FuncTdFiles",
    ],
)

spu_cc_library(
    name = "dialect",
    srcs = glob([
        "*.cc",
    ]),
    hdrs = glob([
        "*.h",
    ]),
    deps = [
        ":attrs_inc_gen",
        ":base_enums_inc_gen",
        ":canonicalization_pattern_inc_gen",
        ":interface",
        ":ops_inc_gen",
        ":types_inc_gen",
        "//libspu/core:prelude",
        "//libspu/dialect/utils",
        "@llvm-project//mlir:FuncDialect",
        "@llvm-project//mlir:IR",
        "@stablehlo//:stablehlo_type_inference",
    ],
)

gentbl_filegroup(
    name = "op_doc",
    tbl_outs = [
        (
            ["-gen-op-doc"],
            "op_doc.md",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "ops.td",
    deps = [":dialect_td_files"],
)
