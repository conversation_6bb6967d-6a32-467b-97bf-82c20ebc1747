// Copyright 2021 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include "mlir/Bytecode/BytecodeOpInterface.h"
#include "mlir/Interfaces/InferTypeOpInterface.h"
#include "mlir/Interfaces/SideEffectInterfaces.h"

#include "libspu/dialect/pphlo/IR/assembly_format.h"

namespace mlir::spu::pphlo::OpTrait {

template <typename ConcreteType>
class PairwiseSameOperandAndResultType
    : public mlir::OpTrait::TraitBase<ConcreteType,
                                      PairwiseSameOperandAndResultType> {
 public:
  static LogicalResult verifyTrait(Operation* op) {
    auto numOperands = op->getNumOperands();
    auto numResults = op->getNumResults();
    if (numOperands != numResults) {
      return op->emitOpError()
             << "requires the same number of operands and results, #op "
             << numOperands << " #ret " << numResults;
    }

    for (auto idx : llvm::seq<unsigned int>(0, numOperands)) {
      if (op->getOperand(idx).getType() != op->getResult(idx).getType()) {
        return op->emitOpError()
               << "requires the same type for operand and result at index "
               << idx;
      }
    }
    return success();
  }
};

}  // namespace mlir::spu::pphlo::OpTrait

// Put it here
#include "libspu/dialect/pphlo/IR/attrs.h"
#include "libspu/dialect/pphlo/IR/types.h"

#define GET_OP_CLASSES
#include "libspu/dialect/pphlo/IR/ops.h.inc"
