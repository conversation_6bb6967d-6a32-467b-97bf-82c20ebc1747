// Copyright 2023 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

#ifndef SPU_DIALECT_PPHLO_INTERFACE
#define SPU_DIALECT_PPHLO_INTERFACE

include "mlir/IR/OpBase.td"

def PPHloTypeInterface : TypeInterface<"PPHloTypeInterface"> {
  let cppNamespace = "mlir::spu::pphlo";

  let description = [{
    Interface for encapsulating the common properties of pphlo types.
  }];

  let methods = [
    InterfaceMethod<
      /*description=*/"Get bit-width of the type.",
      /*retTy=*/"unsigned",
      /*methodName=*/"getWidth"
    >
  ];
}

#endif
