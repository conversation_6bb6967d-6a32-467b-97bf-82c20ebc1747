# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@llvm-project//mlir:tblgen.bzl", "gentbl_cc_library")
load("//bazel:spu.bzl", "spu_cc_library")

gentbl_cc_library(
    name = "pphlo_pass_inc_gen",
    tbl_outs = [
        (
            ["-gen-pass-decls"],
            "passes.h.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "passes.td",
    visibility = [
        "//visibility:private",  # This target is a private detail of pass implementations
    ],
    deps = [
        "@llvm-project//mlir:PassBaseTdFiles",
    ],
)

gentbl_cc_library(
    name = "decompose_patterns_inc_gen",
    tbl_outs = [
        (
            ["-gen-rewriters"],
            "decompose_patterns.cc.inc",
        ),
    ],
    tblgen = "@llvm-project//mlir:mlir-tblgen",
    td_file = "decompose_patterns.td",
    visibility = [
        "//visibility:private",  # This target is a private detail of pass implementations
    ],
    deps = [
        "//libspu/dialect/pphlo/IR:dialect_td_files",
        "@llvm-project//mlir:FuncTdFiles",
    ],
)

spu_cc_library(
    name = "all_passes",
    srcs = glob([
        "*.cc",
    ]),
    hdrs = glob([
        "*.h",
    ]),
    visibility = [
        "//visibility:public",
    ],
    deps = [
        ":decompose_patterns_inc_gen",
        ":pphlo_pass_inc_gen",
        "//libspu/compiler/utils",
        "//libspu/device:intrinsic_table",
        "//libspu/dialect/pphlo/IR:dialect",
        "@llvm-project//mlir:IR",
        "@llvm-project//mlir:TransformUtils",
        "@stablehlo//:stablehlo_ops",
    ],
)
