// Copyright 2021 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include "libspu/core/value.h"

namespace spu {
class SPUContext;
}

namespace spu::kernel::hal {

/// cast dtype
// @param in, the input value
// @param to_type, destination dtype
Value dtype_cast(SPUContext* ctx, const Value& in, DataType to_type);

/// cast public to secret
// @param in, the input value
Value seal(SPUContext* ctx, const Value& x);

/// reveal a secret
// @param in, the input value
Value reveal(SPUContext* ctx, const Value& x);

/// reveal a secret to a specific party
// @param in, the input value
// @param rank, the rank of the party to reveal to
Value reveal_to(SPUContext* ctx, const Value& x, size_t rank);

}  // namespace spu::kernel::hal
