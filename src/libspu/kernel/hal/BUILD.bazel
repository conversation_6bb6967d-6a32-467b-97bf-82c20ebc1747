# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "prot_wrapper",
    srcs = ["prot_wrapper.cc"],
    hdrs = ["prot_wrapper.h"],
    deps = [
        "//libspu/core:context",
        "//libspu/core:value",
        "//libspu/mpc:api",
    ],
)

spu_cc_library(
    name = "ring",
    srcs = ["ring.cc"],
    hdrs = ["ring.h"],
    deps = [
        ":prot_wrapper",
        "//libspu/core:context",
    ],
)

spu_cc_library(
    name = "complex",
    srcs = ["complex.cc"],
    hdrs = ["complex.h"],
    deps = [
        ":constants",
        ":type_cast",
        "//libspu/core:context",
    ],
)

spu_cc_library(
    name = "integer",
    srcs = ["integer.cc"],
    hdrs = ["integer.h"],
    deps = [
        ":prot_wrapper",
        ":ring",
    ],
)

spu_cc_test(
    name = "integer_test",
    srcs = ["integer_test.cc"],
    deps = [
        ":constants",
        ":integer",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_test(
    name = "ring_test",
    srcs = ["ring_test.cc"],
    deps = [
        ":constants",
        ":ring",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "fxp_cleartext",
    srcs = ["fxp_cleartext.cc"],
    hdrs = ["fxp_cleartext.h"],
    deps = [
        "//libspu/core:context",
        "//libspu/core:encoding",
        "//libspu/core:value",
    ],
)

spu_cc_library(
    name = "fxp_base",
    srcs = ["fxp_base.cc"],
    hdrs = ["fxp_base.h"],
    deps = [
        ":constants",
        ":fxp_cleartext",
    ],
)

spu_cc_test(
    name = "fxp_base_test",
    srcs = ["fxp_base_test.cc"],
    deps = [
        ":fxp_base",
        ":type_cast",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "fxp_approx",
    srcs = ["fxp_approx.cc"],
    hdrs = ["fxp_approx.h"],
    deps = [
        ":fxp_base",
        ":fxp_cleartext",
        ":shape_ops",
        ":type_cast",
    ],
)

spu_cc_test(
    name = "fxp_approx_test",
    srcs = ["fxp_approx_test.cc"],
    deps = [
        ":fxp_approx",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "constants",
    srcs = ["constants.cc"],
    hdrs = ["constants.h"],
    deps = [
        ":prot_wrapper",
        ":ring",
        "//libspu/core:context",
        "//libspu/core:encoding",
        "//libspu/core:pt_buffer_view",
        "//libspu/core:value",
        "//libspu/mpc/common:pv2k",  # TODO: this is a bad reference
    ],
)

spu_cc_test(
    name = "constants_test",
    srcs = ["constants_test.cc"],
    deps = [
        ":constants",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "public_helper",
    srcs = ["public_helper.cc"],
    hdrs = ["public_helper.h"],
    deps = [
        "//libspu/core:context",
        "//libspu/core:encoding",
        "//libspu/core:value",
        "//libspu/core:xt_helper",
        "//libspu/mpc/common:pv2k",  # TODO: this is a bad reference
    ],
)

spu_cc_library(
    name = "debug",
    srcs = ["debug.cc"],
    hdrs = ["debug.h"],
    deps = [
        ":constants",  # for dump_public_as
        ":public_helper",
        ":type_cast",
        "//libspu/core:prelude",
    ],
)

spu_cc_library(
    name = "polymorphic",
    srcs = ["polymorphic.cc"],
    hdrs = ["polymorphic.h"],
    deps = [
        ":fxp_approx",
        ":fxp_base",
        ":integer",
        ":shape_ops",
        ":type_cast",
        "//libspu/core:prelude",
        "//libspu/core:vectorize",
    ],
)

spu_cc_test(
    name = "polymorphic_test",
    srcs = ["polymorphic_test.cc"],
    deps = [
        ":polymorphic",
        "//libspu/core:context",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:linalg",
    ],
)

spu_cc_test(
    name = "array_element_wise_test",
    srcs = ["array_element_wise_test.cc"],
    deps = [
        ":polymorphic",
        "//libspu/core:context",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "type_cast",
    srcs = ["type_cast.cc"],
    hdrs = ["type_cast.h"],
    deps = [
        ":random",
        ":ring",
        "//libspu/core:prelude",
    ],
)

spu_cc_library(
    name = "utils",
    srcs = ["utils.cc"],
    hdrs = ["utils.h"],
    deps = [
        ":constants",
        ":ring",
        ":shape_ops",
        "//libspu/core:prelude",
    ],
)

spu_cc_test(
    name = "utils_test",
    srcs = ["utils_test.cc"],
    deps = [
        ":polymorphic",
        ":utils",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_test(
    name = "type_cast_test",
    srcs = ["type_cast_test.cc"],
    deps = [
        ":type_cast",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "random",
    srcs = ["random.cc"],
    hdrs = ["random.h"],
    deps = [
        ":constants",
        ":public_helper",
        "//libspu/core:context",
        "//libspu/core:prelude",
    ],
)

spu_cc_library(
    name = "shape_ops",
    srcs = ["shape_ops.cc"],
    hdrs = ["shape_ops.h"],
    deps = [
        # Please DONT add extra dependency here.
        ":prot_wrapper",
        ":ring",
        "//libspu/core:context",
    ],
)

spu_cc_library(
    name = "permute",
    srcs = ["permute.cc"],
    hdrs = ["permute.h"],
    deps = [
        ":polymorphic",
        ":public_helper",
        ":ring",
        ":shape_ops",
        ":utils",
        "//libspu/core:context",
    ],
)

spu_cc_test(
    name = "shape_ops_test",
    srcs = ["shape_ops_test.cc"],
    deps = [
        ":shape_ops",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "soprf",
    srcs = ["soprf.cc"],
    hdrs = ["soprf.h"],
    deps = [
        ":ring",
    ],
)
