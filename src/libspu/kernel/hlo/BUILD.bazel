# Copyright 2022 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "basic_binary",
    srcs = ["basic_binary.cc"],
    hdrs = ["basic_binary.h"],
    deps = [
        ":utils",
        "//libspu/kernel/hal:complex",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:utils",
    ],
)

spu_cc_test(
    name = "basic_binary_test",
    srcs = ["basic_binary_test.cc"],
    deps = [
        ":basic_binary",
        ":casting",
        ":const",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "basic_ternary",
    srcs = ["basic_ternary.cc"],
    hdrs = ["basic_ternary.h"],
    deps = [
        ":casting",
        ":const",
        ":utils",
        "//libspu/kernel/hal:complex",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:ring",
    ],
)

spu_cc_test(
    name = "basic_ternary_test",
    srcs = ["basic_ternary_test.cc"],
    deps = [
        ":basic_ternary",
        ":const",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "basic_unary",
    srcs = ["basic_unary.cc"],
    hdrs = ["basic_unary.h"],
    deps = [
        ":utils",
        "//libspu/kernel/hal:complex",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:type_cast",
    ],
)

spu_cc_test(
    name = "basic_unary_test",
    srcs = ["basic_unary_test.cc"],
    deps = [
        ":basic_unary",
        ":casting",
        ":const",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "casting",
    srcs = ["casting.cc"],
    hdrs = ["casting.h"],
    deps = [
        ":utils",
        "//libspu/kernel/hal:complex",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:type_cast",
    ],
)

spu_cc_test(
    name = "casting_test",
    srcs = ["casting_test.cc"],
    deps = [
        ":casting",
        ":const",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "const",
    srcs = ["const.cc"],
    hdrs = ["const.h"],
    deps = [
        ":utils",
        "//libspu/core:encoding",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:shape_ops",
    ],
)

spu_cc_test(
    name = "const_test",
    srcs = ["const_test.cc"],
    deps = [
        ":const",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "control_flow",
    srcs = ["control_flow.cc"],
    hdrs = ["control_flow.h"],
    deps = [
        ":const",
        ":utils",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:public_helper",
        "//libspu/kernel/hal:shape_ops",
        "//libspu/kernel/hal:type_cast",
    ],
)

spu_cc_library(
    name = "convolution",
    srcs = ["convolution.cc"],
    hdrs = ["convolution.h"],
    deps = [
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:shape_ops",
    ],
)

spu_cc_library(
    name = "indexing",
    srcs = ["indexing.cc"],
    hdrs = ["indexing.h"],
    deps = [
        ":basic_unary",
        ":const",
        ":utils",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:ring",
        "//libspu/kernel/hal:shape_ops",
        "//libspu/kernel/hal:type_cast",
        "//libspu/kernel/hal:utils",
    ],
)

spu_cc_test(
    name = "indexing_test",
    srcs = ["indexing_test.cc"],
    deps = [
        ":casting",
        ":indexing",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "geometrical",
    srcs = ["geometrical.cc"],
    hdrs = ["geometrical.h"],
    deps = [
        "//libspu/kernel/hal:complex",
        "//libspu/kernel/hal:shape_ops",
    ],
)

spu_cc_test(
    name = "geometrical_test",
    srcs = ["geometrical_test.cc"],
    deps = [
        ":basic_binary",
        ":casting",
        ":const",
        ":geometrical",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "rand",
    srcs = ["rand.cc"],
    hdrs = ["rand.h"],
    deps = [
        "//libspu/kernel/hal:random",
    ],
)

spu_cc_library(
    name = "rank",
    srcs = ["rank.cc"],
    hdrs = ["rank.h"],
    deps = [
        "//libspu/kernel/hal:permute",
    ],
)

spu_cc_test(
    name = "rank_test",
    srcs = ["rank_test.cc"],
    deps = [
        ":rank",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "reduce",
    srcs = ["reduce.cc"],
    hdrs = ["reduce.h"],
    deps = [
        ":utils",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:ring",
        "//libspu/kernel/hal:shape_ops",
        "//libspu/kernel/hal:utils",
    ],
)

spu_cc_library(
    name = "select_and_scatter",
    srcs = ["select_and_scatter.cc"],
    hdrs = ["select_and_scatter.h"],
    deps = [
        ":const",
        ":reduce",
        ":utils",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:shape_ops",
    ],
)

spu_cc_test(
    name = "select_and_scatter_test",
    srcs = ["select_and_scatter_test.cc"],
    deps = [
        ":select_and_scatter",
        "//libspu/kernel:test_util",
    ],
)

spu_cc_library(
    name = "shift",
    srcs = ["shift.cc"],
    hdrs = ["shift.h"],
    deps = [
        ":utils",
        "//libspu/kernel/hal:constants",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:public_helper",
    ],
)

spu_cc_library(
    name = "sort",
    srcs = ["sort.cc"],
    hdrs = ["sort.h"],
    deps = [
        "//libspu/kernel/hal:permute",
    ],
)

spu_cc_test(
    name = "sort_test",
    srcs = ["sort_test.cc"],
    deps = [
        ":casting",
        ":const",
        ":sort",
        "//libspu/kernel:test_util",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/mpc/utils:simulate",
        "@magic_enum",
    ],
)

spu_cc_library(
    name = "utils",
    srcs = ["utils.cc"],
    hdrs = ["utils.h"],
    deps = [
        "//libspu/kernel/hal:public_helper",
        "//libspu/kernel/hal:shape_ops",
        "//libspu/kernel/hal:utils",
    ],
)

spu_cc_test(
    name = "utils_test",
    srcs = ["utils_test.cc"],
    deps = [
        ":utils",
    ],
)

spu_cc_library(
    name = "shuffle",
    srcs = ["shuffle.cc"],
    hdrs = ["shuffle.h"],
    deps = [
        ":sort",
        "//libspu/core:context",
        "//libspu/kernel/hal:permute",
        "//libspu/kernel/hal:polymorphic",
        "//libspu/kernel/hal:prot_wrapper",
        "//libspu/kernel/hal:random",
    ],
)

spu_cc_test(
    name = "shuffle_test",
    srcs = ["shuffle_test.cc"],
    deps = [
        ":shuffle",
        "//libspu/kernel:test_util",
        "//libspu/kernel/hlo:casting",
        "//libspu/kernel/hlo:const",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "soprf",
    srcs = ["soprf.cc"],
    hdrs = ["soprf.h"],
    deps = [
        ":geometrical",
        "//libspu/kernel/hal:soprf",
    ],
)

spu_cc_test(
    name = "soprf_test",
    srcs = ["soprf_test.cc"],
    deps = [
        ":casting",
        ":const",
        ":soprf",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "permute",
    srcs = ["permute.cc"],
    hdrs = ["permute.h"],
    deps = [
        "//libspu/kernel/hal:permute",
    ],
)

spu_cc_test(
    name = "permute_test",
    srcs = ["permute_test.cc"],
    deps = [
        ":casting",
        ":const",
        ":permute",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
    ],
)
