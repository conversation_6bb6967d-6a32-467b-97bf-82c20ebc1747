# Copyright 2023 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_cuda//cuda:defs.bzl", "cuda_library")
load("//bazel:spu.bzl", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

cuda_library(
    name = "kernels",
    srcs = ["kernels.cu"],
    hdrs = ["kernels.h"],
    tags = [
        "manual",  # Exclude this target from :all expansion
    ],
    deps = [
        "@cutlass",
    ],
)

cuda_library(
    name = "utils",
    srcs = ["utils.cu"],
    hdrs = ["utils.h"],
    tags = [
        "manual",  # Exclude this target from :all expansion
    ],
    deps = [
        "//libspu/core:shape",
    ],
)

spu_cc_test(
    name = "utils_test",
    srcs = ["utils_test.cc"],
    tags = [
        "manual",  # Exclude this target from :all expansion
    ],
    target_compatible_with = select({
        "@rules_cuda//cuda:is_enabled": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    deps = [
        ":utils",
    ],
)

spu_cc_test(
    name = "kernels_test",
    srcs = ["kernels_test.cc"],
    tags = [
        "manual",  # Exclude this target from :all expansion
    ],
    target_compatible_with = select({
        "@rules_cuda//cuda:is_enabled": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
    deps = [
        ":kernels",
        ":utils",
    ],
)
