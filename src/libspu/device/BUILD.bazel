# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "device",
    deps = [
        ":api",
        ":io",
    ],
)

spu_cc_library(
    name = "io",
    srcs = ["io.cc"],
    hdrs = ["io.h"],
    deps = [
        ":symbol_table",
        "//libspu:spu",
        "//libspu/core:context",
        "//libspu/core:pt_buffer_view",
        "//libspu/core:value",
        "//libspu/kernel/hal:public_helper",
        "//libspu/mpc:factory",
    ],
)

spu_cc_test(
    name = "io_test",
    srcs = ["io_test.cc"],
    deps = [
        ":io",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "executor",
    srcs = ["executor.cc"],
    hdrs = ["executor.h"],
    deps = [
        ":intrinsic_table",
        ":symbol_table",
        "//libspu/core:context",
        "//libspu/core:value",
        "//libspu/dialect/pphlo/IR:dialect",
        "@llvm-project//mlir:IR",
    ],
)

spu_cc_library(
    name = "api",
    srcs = ["api.cc"],
    hdrs = ["api.h"],
    deps = [
        ":executor",
        "//libspu:version",
        "//libspu/device/pphlo:pphlo_executor",
        "//libspu/device/utils:debug_dump_constant",
        "//libspu/dialect/utils",
        "@llvm-project//mlir:FuncDialect",
        "@llvm-project//mlir:IR",
        "@llvm-project//mlir:Parser",
    ],
)

spu_cc_library(
    name = "test_utils",
    hdrs = ["test_utils.h"],
    deps = [
        ":io",
        ":symbol_table",
        "//libspu:spu",
        "//libspu/core:ndarray_ref",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "symbol_table",
    srcs = ["symbol_table.cc"],
    hdrs = ["symbol_table.h"],
    deps = [
        "//libspu/core:pt_buffer_view",
        "//libspu/core:value",
    ],
)

spu_cc_library(
    name = "intrinsic_table",
    hdrs = ["intrinsic_table.h"],
)
