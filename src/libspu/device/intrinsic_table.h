// Copyright 2024 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

// clang-format off

//         MACRO_NAME       FCN_NAME
#define    TOPK             "mhlo.topk"
#define    ERF              "mhlo.erf"
#define    PREFER_A         "spu.prefer_a"
#define    DBG_PRINT        "spu.dbg_print"
#define    GATHER           "spu.gather"
// should be consistent with python level
#define    MAKE_CACHED_VAR  "spu.make_cached_var"
#define    DROP_CACHED_VAR  "spu.drop_cached_var"
#define    REVEAL           "spu.reveal"

// clang-format on
