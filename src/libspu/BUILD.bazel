# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//bazel:spu.bzl", "spu_cc_library")

package(default_visibility = ["//visibility:public"])

proto_library(
    name = "spu_proto",
    srcs = ["spu.proto"],
)

cc_proto_library(
    name = "spu_cc_proto",
    deps = [":spu_proto"],
)

spu_cc_library(
    name = "spu",
    srcs = ["spu.cc"],
    hdrs = ["spu.h"],
    deps = [
        ":spu_cc_proto",
        "@magic_enum",
        "@protobuf",
    ],
)

spu_cc_library(
    name = "version",
    hdrs = ["version.h"],
)
