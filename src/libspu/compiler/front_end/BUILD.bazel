# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@yacl//bazel:yacl.bzl", "OMP_DEPS")
load("//bazel:spu.bzl", "spu_cc_library")

spu_cc_library(
    name = "hlo_importer",
    srcs = ["hlo_importer.cc"],
    hdrs = ["hlo_importer.h"],
    copts = [
        # TF headers have lots of warnings, disable warning-as-error for this compilation unit
        "-Wno-error",
    ],
    visibility = ["//visibility:private"],  # Should not use this library directly
    deps = [
        "//libspu/compiler/common:compilation_context",
        "//libspu/core:prelude",
        "@xla//xla/service:algebraic_simplifier",
        "@xla//xla/service:batch_dot_simplification",
        "@xla//xla/service:batchnorm_expander",
        "@xla//xla/service:bitcast_dtypes_expander",
        "@xla//xla/service:call_inliner",
        "@xla//xla/service:cholesky_expander",
        "@xla//xla/service:conditional_simplifier",
        "@xla//xla/service:conditional_to_select",
        "@xla//xla/service:convolution_4d_expander",
        "@xla//xla/service:convolution_group_converter",
        "@xla//xla/service:dot_decomposer",
        "@xla//xla/service:eigh_expander",
        "@xla//xla/service:float_normalization",
        "@xla//xla/service:gather_expander",
        "@xla//xla/service:gather_simplifier",
        "@xla//xla/service:hlo_constant_folding",
        "@xla//xla/service:hlo_cse",
        "@xla//xla/service:hlo_dce",
        "@xla//xla/service:hlo_pass_pipeline",
        "@xla//xla/service:map_inliner",
        "@xla//xla/service:operand_upcaster",
        "@xla//xla/service:qr_expander",
        "@xla//xla/service:real_imag_expander",
        "@xla//xla/service:reshape_mover",
        "@xla//xla/service:result_caster",
        "@xla//xla/service:scatter_expander",
        "@xla//xla/service:slice_sinker",
        "@xla//xla/service:sort_simplifier",
        "@xla//xla/service:triangular_solve_expander",
        "@xla//xla/service:while_loop_constant_sinking",
        "@xla//xla/service:while_loop_simplifier",
        "@xla//xla/service:zero_sized_hlo_elimination",
        "@xla//xla/service/gpu/transforms:dot_dimension_sorter",
        "@xla//xla/translate/hlo_to_mhlo:hlo_module_importer",
    ] + OMP_DEPS,
)

spu_cc_library(
    name = "fe",
    srcs = ["fe.cc"],
    hdrs = ["fe.h"],
    visibility = ["//visibility:public"],
    deps = [
        ":hlo_importer",
        "//libspu/compiler/common:compilation_context",
        "//libspu/compiler/utils",
        "//libspu/dialect/pphlo/transforms:all_passes",
        "@llvm-project//mlir:FuncExtensions",
        "@llvm-project//mlir:Parser",
        "@magic_enum",
        "@xla//xla/mlir_hlo:mhlo_passes",
        "@xla//xla/translate/mhlo_to_hlo:translate",
    ],
)
