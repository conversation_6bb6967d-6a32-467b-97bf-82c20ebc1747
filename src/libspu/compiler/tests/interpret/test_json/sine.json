{"name": "sine", "template": "basic_unary", "testcases": [{"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.140630]", "shape": "5", "dtype": "f16"}], "expected": [{"data": "[0.000000e+00, 8.413080e-01, 1.246950e-01, 9.979240e-02, 9.675020e-04]", "shape": "5", "dtype": "f16"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159274]", "shape": "5", "dtype": "f32"}], "expected": [{"data": "[0.000000e+00, 0.841470957, 0.12467473, 0.0998334214, -8.74227765E-8]", "shape": "5", "dtype": "f32"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.1415926535897931]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[0.000000e+00, 0.8414709848078965, 0.12467473338522769, 0.099833416646828154, 1.2246467991473532E-16]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq"}]}