{"name": "cosine", "template": "basic_unary", "testcases": [{"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.140630]", "shape": "5", "dtype": "f16"}], "expected": [{"data": "[1.000000e+00, 0.540302277, 0.992197692, 0.995004177, -1.000000e+00]", "shape": "5", "dtype": "f16"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159274]", "shape": "5", "dtype": "f32"}], "expected": [{"data": "[1.000000e+00, 0.540302277, 0.992197692, 0.995004177, -1.000000e+00]", "shape": "5", "dtype": "f32"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.1415926535897931]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[1.000000e+00, 0.54030230586813977, 0.992197667229329, 0.99500416527802582, -1.000000e+00]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq"}]}