{"name": "multiply", "template": "basic_binary", "testcases": [{"inputs": [{"data": "[0, 1, 8, -9, 0]", "shape": "5", "dtype": "i8"}, {"data": "[-128, -1, 8, -9, 127]", "shape": "5", "dtype": "i8"}], "expected": [{"data": "[0, -1, 64, 81, 0]", "shape": "5", "dtype": "i8"}]}, {"inputs": [{"data": "[0, 16, 16]", "shape": "3", "dtype": "ui8"}, {"data": "[255, 16, 17]", "shape": "3", "dtype": "ui8"}], "expected": [{"data": "[0, 0, 16]", "shape": "3", "dtype": "ui8"}]}, {"inputs": [{"data": "[0, 1, 128, -129, 0]", "shape": "5", "dtype": "i16"}, {"data": "[-32768, -1, 128, -129, 32767]", "shape": "5", "dtype": "i16"}], "expected": [{"data": "[0, -1, 16384, 16641, 0]", "shape": "5", "dtype": "i16"}]}, {"inputs": [{"data": "[0, 256]", "shape": "2", "dtype": "ui16"}, {"data": "[65535, 256]", "shape": "2", "dtype": "ui16"}], "expected": [{"data": "[0, 0]", "shape": "2", "dtype": "ui16"}]}, {"inputs": [{"data": "[0, 1, 32768, -32769, 0]", "shape": "5", "dtype": "i32"}, {"data": "[-2147483648, -1, 32768, -32769, 2147483647]", "shape": "5", "dtype": "i32"}], "expected": [{"data": "[0, -1, 1073741824, 1073807361, 0]", "shape": "5", "dtype": "i32"}]}, {"inputs": [{"data": "[0, 65536]", "shape": "2", "dtype": "ui32"}, {"data": "[4294967295, 65536]", "shape": "2", "dtype": "ui32"}], "expected": [{"data": "[0, 0]", "shape": "2", "dtype": "ui32"}]}, {"inputs": [{"data": "[0, 1, 2147483648, -2147483649, 0]", "shape": "5", "dtype": "i64"}, {"data": "[-9223372036854775808, -1, 2147483648, -2147483649, 9223372036854775807]", "shape": "5", "dtype": "i64"}], "expected": [{"data": "[0, -1, 4611686018427387904, 4611686022722355201, 0]", "shape": "5", "dtype": "i64"}]}, {"inputs": [{"data": "[0, 4294967296]", "shape": "2", "dtype": "ui64"}, {"data": "[8446744073709551615, 4294967296]", "shape": "2", "dtype": "ui64"}], "expected": [{"data": "[0, 0]", "shape": "2", "dtype": "ui64"}]}, {"inputs": [{"data": "[false, false, true, true]", "shape": "4", "dtype": "i1"}, {"data": "[false, true, false, true]", "shape": "4", "dtype": "i1"}], "expected": [{"data": "[false, false, false, true]", "shape": "4", "dtype": "i1"}]}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.141]", "shape": "5", "dtype": "f16"}, {"data": "[0.0, 7.0, 0.75,  0.3, 3.141]", "shape": "5", "dtype": "f16"}], "expected": [{"data": "[0.000000e+00, 7.000000e+00, 9.375000e-02, 2.999880e-02, 9.867180e+00]", "shape": "5", "dtype": "f16"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159265]", "shape": "5", "dtype": "f32"}, {"data": "[0.0, 7.0,  0.75, 0.3, 3.14159265]", "shape": "5", "dtype": "f32"}], "expected": [{"data": "[0.000000e+00, 7.000000e+00, 9.375000e-02, 0.0300000012, 9.86960506]", "shape": "5", "dtype": "f32"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159265358979323846]", "shape": "5", "dtype": "f64"}, {"data": "[0.0, 7.0, 0.75,  0.3, 3.14159265358979323846]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[0.000000e+00, 7.000000e+00, 9.375000e-02, 3.000000e-02, 9.869604401089358]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq"}]}