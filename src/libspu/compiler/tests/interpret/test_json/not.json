{"name": "not", "template": "basic_unary", "testcases": [{"inputs": [{"data": "[127, -128, 0]", "shape": "3", "dtype": "i8"}], "expected": [{"data": "[-128, 127, -1]", "shape": "3", "dtype": "i8"}]}, {"inputs": [{"data": "[0, 127, 255]", "shape": "3", "dtype": "ui8"}], "expected": [{"data": "[255, 128, 0]", "shape": "3", "dtype": "ui8"}]}, {"inputs": [{"data": "[32767, -32768, 0]", "shape": "3", "dtype": "i16"}], "expected": [{"data": "[-32768, 32767, -1]", "shape": "3", "dtype": "i16"}]}, {"inputs": [{"data": "[0, 32767, 65535]", "shape": "3", "dtype": "ui16"}], "expected": [{"data": "[65535, 32768, 0]", "shape": "3", "dtype": "ui16"}]}, {"inputs": [{"data": "[2147483647, -2147483648, 0]", "shape": "3", "dtype": "i32"}], "expected": [{"data": "[-2147483648, 2147483647, -1]", "shape": "3", "dtype": "i32"}]}, {"inputs": [{"data": "[0, 2147483647, 4294967295]", "shape": "3", "dtype": "ui32"}], "expected": [{"data": "[4294967295, 2147483648, 0]", "shape": "3", "dtype": "ui32"}]}, {"inputs": [{"data": "[9223372036854775807, -9223372036854775808, 0]", "shape": "3", "dtype": "i64"}], "expected": [{"data": "[-9223372036854775808, 9223372036854775807, -1]", "shape": "3", "dtype": "i64"}]}, {"inputs": [{"data": "[0, 9223372036854775807, 18446744073709551615]", "shape": "3", "dtype": "ui64"}], "expected": [{"data": "[18446744073709551615, 9223372036854775808, 0]", "shape": "3", "dtype": "ui64"}]}, {"inputs": [{"data": "[false, true]", "shape": "2", "dtype": "i1"}], "expected": [{"data": "[true, false]", "shape": "2", "dtype": "i1"}]}, {"inputs": [{"data": "false", "shape": "", "dtype": "i1"}], "expected": [{"data": "true", "shape": "", "dtype": "i1"}]}, {"inputs": [{"data": "true", "shape": "", "dtype": "i1"}], "expected": [{"data": "false", "shape": "", "dtype": "i1"}]}]}