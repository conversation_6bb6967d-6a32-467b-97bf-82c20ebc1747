{"name": "power", "template": "basic_binary", "testcases": [{"inputs": [{"data": "[-1, -1, -3, 1, -3, 0]", "shape": "6", "dtype": "i64"}, {"data": "[1, 0, -3, -3, 3, 2]", "shape": "6", "dtype": "i64"}], "expected": [{"data": "[-1, 1, 0, 1, -27, 0]", "shape": "6", "dtype": "i64"}]}, {"inputs": [{"data": "[0, 0, 1, 1, 5]", "shape": "5", "dtype": "ui64"}, {"data": "[0, 1, 0, 2, 5]", "shape": "5", "dtype": "ui64"}], "expected": [{"data": "[1, 0, 1, 1, 3125]", "shape": "5", "dtype": "ui64"}]}, {"inputs": [{"data": "[-2.0, -0.0, 5.0, 3.0, 10000.0]", "shape": "5", "dtype": "f64"}, {"data": "[2.0, 2.0, 2.0, -1.0, 1.0]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[4.000000e+00, 0.000000e+00, 2.500000e+01, 0.33333333333333331, 10000.0]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq", "tol": 0.6}]}