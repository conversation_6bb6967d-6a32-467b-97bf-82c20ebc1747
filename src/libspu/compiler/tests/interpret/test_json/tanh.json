{"name": "tanh", "template": "basic_unary", "testcases": [{"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.140630]", "shape": "5", "dtype": "f16"}], "expected": [{"data": "[0.000000e+00, 7.617180e-01, 1.243290e-01, 9.967040e-02, 9.960930e-01]", "shape": "5", "dtype": "f16"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159274]", "shape": "5", "dtype": "f32"}], "expected": [{"data": "[0.000000e+00, 0.761594176, 1.243530e-01, 0.0996679961, 0.996272087]", "shape": "5", "dtype": "f32"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.1415926535897931]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[0.000000e+00, 0.76159415595576485, 0.12435300177159619, 0.099667994624955819, 0.99627207622074998]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq"}]}