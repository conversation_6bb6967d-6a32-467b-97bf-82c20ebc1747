{"name": "negate", "template": "basic_unary", "testcases": [{"inputs": [{"data": "[-128, -9, 0, 8, 127]", "shape": "5", "dtype": "i8"}], "expected": [{"data": "[-128, 9, 0, -8, -127]", "shape": "5", "dtype": "i8"}]}, {"inputs": [{"data": "[0, 16, 255]", "shape": "3", "dtype": "ui8"}], "expected": [{"data": "[0, 240, 1]", "shape": "3", "dtype": "ui8"}]}, {"inputs": [{"data": "[-32768, -129, 0, 128, 32767]", "shape": "5", "dtype": "i16"}], "expected": [{"data": "[-32768, 129, 0, -128, -32767]", "shape": "5", "dtype": "i16"}]}, {"inputs": [{"data": "[0, 256, 65535]", "shape": "3", "dtype": "ui16"}], "expected": [{"data": "[0, 65280, 1]", "shape": "3", "dtype": "ui16"}]}, {"inputs": [{"data": "[-2147483648, -65537, 0, 65536, 2147483647]", "shape": "5", "dtype": "i32"}], "expected": [{"data": "[-2147483648, 65537, 0, -65536, -2147483647]", "shape": "5", "dtype": "i32"}]}, {"inputs": [{"data": "[0, 65536, 4294967295]", "shape": "3", "dtype": "ui32"}], "expected": [{"data": "[0, 4294901760, 1]", "shape": "3", "dtype": "ui32"}]}, {"inputs": [{"data": "[-9223372036854775808, -2147483649, 0, 2147483648, 9223372036854775807]", "shape": "5", "dtype": "i64"}], "expected": [{"data": "[-9223372036854775808, 2147483649, 0, -2147483648, -9223372036854775807]", "shape": "5", "dtype": "i64"}]}, {"inputs": [{"data": "[0, 4294967296, 18446744073709551615]", "shape": "3", "dtype": "ui64"}], "expected": [{"data": "[0, 18446744069414584320, 1]", "shape": "3", "dtype": "ui64"}]}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.140630]", "shape": "5", "dtype": "f16"}], "expected": [{"data": "[0.000000e+00, -1.000000e+00, -1.250000e-01, -9.997550e-02, -3.140630e+00]", "shape": "5", "dtype": "f16"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.14159274]", "shape": "5", "dtype": "f32"}], "expected": [{"data": "[0.000000e+00, -1.000000e+00, -1.250000e-01, -1.000000e-01, -3.14159274]", "shape": "5", "dtype": "f32"}], "checker": "expect_almost_eq"}, {"inputs": [{"data": "[0.0, 1.0, 0.125, 0.1, 3.1415926535897931]", "shape": "5", "dtype": "f64"}], "expected": [{"data": "[0.000000e+00, -1.000000e+00, -1.250000e-01, -1.000000e-01, -3.1415926535897931]", "shape": "5", "dtype": "f64"}], "checker": "expect_almost_eq"}]}