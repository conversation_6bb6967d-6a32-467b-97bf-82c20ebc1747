# Copyright 2020 The TensorFlow Authors. All Rights Reserved.
# Copyright 2022 The StableHLO Authors.
# Copyright 2023 SecretFlow Authors.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
@LIT_SITE_CFG_IN_HEADER@
import lit.llvm
lit.llvm.initialize(lit_config, config)
config.llvm_tools_dir = "@LLVM_TOOLS_DIR@"
config.pphlo_tools_dir = "@PPHLO_TOOLS_DIR@"
lit_config.load_config(config, "@PPHLO_SOURCE_DIR@" + "/libspu/compiler/tests/lit.cfg.py")
