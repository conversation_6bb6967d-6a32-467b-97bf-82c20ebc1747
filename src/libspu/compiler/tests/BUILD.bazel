# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@bazel_skylib//rules:expand_template.bzl", "expand_template")
load("@llvm-project//llvm:lit_test.bzl", "lit_test", "package_path")

# Equivalent of configure_lit_site_cfg from CMakeLists.txt.
expand_template(
    name = "lit_site_cfg_py_gen",
    testonly = True,
    out = "lit.site.cfg.py",
    substitutions = {
        "@LIT_SITE_CFG_IN_HEADER@": "# Autogenerated, do not edit.",
        "@LLVM_TOOLS_DIR@": package_path("@llvm-project//llvm:BUILD"),
        "\"@PPHLO_TOOLS_DIR@\"": "os.path.join(os.environ['TEST_SRCDIR'], '_main', 'libspu', 'compiler', 'tools')",
        "\"@PPHLO_SOURCE_DIR@\"": "os.path.join(os.environ['TEST_SRCDIR'], '_main')",
    },
    template = "lit.site.cfg.py.in",
)

# Equivalent of add_lit_testsuite from CMakeLists.txt.
[
    lit_test(
        name = "%s.test" % src,
        size = "small",
        srcs = [src],
        data = [
            "lit.cfg.py",
            "lit.site.cfg.py",
            "//libspu/compiler/tools:spu-opt",
            "//libspu/compiler/tools:spu-translate",
            "@llvm-project//llvm:FileCheck",
            "@llvm-project//llvm:not",
        ] + glob(["%s.bc" % src]),
        tags = ["pphlo_tests"],
    )
    for src in glob(["**/**/*.mlir"])
]

test_suite(
    name = "pphlo_tests",
    tags = ["pphlo_tests"],
)
