# MLIR-PPHLO: A MLIR based Secure HLO compiler

This implements a self-contained compiler for a privacy preserving linear algebra set of operations inspired by XLA HLO IR using MLIR components.
It is designed to provide an compiler engine for SPU.

Coding practice and conventions in this repository follow the [MLIR Developer Guide](https://mlir.llvm.org/getting_started/DeveloperGuide/) in
this repo as part of the intent to act as an incubator for technology to upstream.
