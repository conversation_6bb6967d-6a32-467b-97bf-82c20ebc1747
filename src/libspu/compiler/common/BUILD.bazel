# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "ir_printer_config",
    srcs = ["ir_printer_config.cc"],
    hdrs = ["ir_printer_config.h"],
    visibility = ["//visibility:private"],
    deps = [
        "@fmt",
        "@llvm-project//mlir:Pass",
    ],
)

spu_cc_library(
    name = "compilation_context",
    srcs = ["compilation_context.cc"],
    hdrs = ["compilation_context.h"],
    deps = [
        ":ir_printer_config",
        "//libspu:spu",
        "//libspu/core:prelude",
    ],
)
