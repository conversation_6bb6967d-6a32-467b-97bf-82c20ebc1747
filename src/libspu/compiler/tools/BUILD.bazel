# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_binary")

package(
    default_visibility = ["//visibility:public"],
)

spu_cc_binary(
    name = "spu-opt",
    srcs = [
        "spu-opt.cc",
    ],
    deps = [
        "//libspu/dialect/pphlo/transforms:all_passes",
        "@llvm-project//llvm:Support",
        "@llvm-project//mlir:FuncExtensions",
        "@llvm-project//mlir:IR",
        "@llvm-project//mlir:MlirOptLib",
        "@llvm-project//mlir:Pass",
        "@xla//xla/mlir_hlo",
        "@xla//xla/mlir_hlo:mhlo_passes",
    ],
)

spu_cc_binary(
    name = "spu-lsp",
    srcs = [
        "spu-lsp.cc",
    ],
    deps = [
        "//libspu/dialect/pphlo/IR:dialect",
        "@llvm-project//llvm:Support",
        "@llvm-project//mlir:FuncExtensions",
        "@llvm-project//mlir:IR",
        "@llvm-project//mlir:MlirLspServerLib",
        "@stablehlo//:stablehlo_ops",
        "@xla//xla/mlir_hlo",
    ],
)

spu_cc_binary(
    name = "spu-translate",
    srcs = [
        "spu-translate.cc",
    ],
    deps = [
        "//libspu/compiler/common:compilation_context",
        "//libspu/compiler/core",
        "//libspu/compiler/utils",
        "//libspu/device/pphlo:pphlo_executor",
        "//libspu/dialect/pphlo/transforms:all_passes",
        "//libspu/dialect/utils",
        "//libspu/kernel:test_util",
        "//libspu/mpc/utils:simulate",
        "@llvm-project//llvm:Support",
        "@llvm-project//mlir:FuncExtensions",
        "@llvm-project//mlir:IR",
        "@llvm-project//mlir:Pass",
        "@llvm-project//mlir:Transforms",
        "@llvm-project//mlir:TranslateLib",
    ],
)
