# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_binary", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "trace",
    srcs = ["trace.cc"],
    hdrs = ["trace.h"],
    deps = [
        "//libspu/core:prelude",
        "@yacl//yacl/link",
    ],
)

spu_cc_test(
    name = "trace_test",
    srcs = ["trace_test.cc"],
    deps = [
        ":trace",
        "//libspu/core:prelude",
    ],
)

spu_cc_library(
    name = "prelude",
    srcs = [],
    hdrs = ["prelude.h"],
    deps = [
        "//libspu:spu",
        "@yacl//yacl/base:exception",
        "@yacl//yacl/utils:scope_guard",
    ],
)

spu_cc_library(
    name = "type_util",
    srcs = ["type_util.cc"],
    hdrs = ["type_util.h"],
    deps = [
        ":half",
        "//libspu:spu",
        "//libspu/core:prelude",
        "@magic_enum",
        "@yacl//yacl/base:int128",
    ],
)

spu_cc_library(
    name = "encoding",
    srcs = ["encoding.cc"],
    hdrs = ["encoding.h"],
    deps = [
        ":ndarray_ref",
        ":parallel_utils",
        ":pt_buffer_view",
        "@yacl//yacl/crypto/tools:prg",
    ],
)

spu_cc_library(
    name = "config",
    srcs = ["config.cc"],
    hdrs = ["config.h"],
    deps = [
        "//libspu:spu",
        "//libspu/core:prelude",
        "@yacl//yacl/utils:parallel",
    ],
)

spu_cc_test(
    name = "encoding_test",
    srcs = ["encoding_test.cc"],
    deps = [
        ":config",
        ":encoding",
        ":xt_helper",
    ],
)

spu_cc_library(
    name = "shape",
    srcs = ["shape.cc"],
    hdrs = ["shape.h"],
    deps = [
        "//libspu/core:prelude",
        "@llvm-project//llvm:Support",
    ],
)

spu_cc_test(
    name = "shape_test",
    srcs = ["shape_test.cc"],
    deps = [
        ":shape",
    ],
)

spu_cc_library(
    name = "type",
    srcs = ["type.cc"],
    hdrs = ["type.h"],
    deps = [
        ":type_util",
        "//libspu/core:prelude",
        "@magic_enum",
    ],
)

spu_cc_test(
    name = "type_test",
    srcs = ["type_test.cc"],
    deps = [
        ":type",
    ],
)

spu_cc_library(
    name = "ndarray_ref",
    srcs = ["ndarray_ref.cc"],
    hdrs = ["ndarray_ref.h"],
    deps = [
        ":bit_utils",
        ":parallel_utils",
        ":shape",
        ":type",
        ":vectorize",
        "//libspu/core:prelude",
        "@yacl//yacl/base:buffer",
    ],
)

spu_cc_test(
    name = "ndarray_ref_test",
    srcs = ["ndarray_ref_test.cc"],
    deps = [
        ":ndarray_ref",
    ],
)

spu_cc_library(
    name = "pt_buffer_view",
    srcs = ["pt_buffer_view.cc"],
    hdrs = ["pt_buffer_view.h"],
    deps = [
        ":ndarray_ref",
        ":shape",
    ],
)

spu_cc_test(
    name = "pt_buffer_view_test",
    srcs = ["pt_buffer_view_test.cc"],
    deps = [
        ":pt_buffer_view",
    ],
)

spu_cc_library(
    name = "xt_helper",
    hdrs = ["xt_helper.h"],
    deps = [
        ":ndarray_ref",
        ":pt_buffer_view",
        ":shape",
        "@xtensor",
    ],
)

spu_cc_test(
    name = "xt_helper_test",
    srcs = ["xt_helper_test.cc"],
    deps = [
        ":xt_helper",
    ],
)

spu_cc_library(
    name = "vectorize",
    hdrs = ["vectorize.h"],
    deps = [
        "//libspu/core:prelude",
    ],
)

spu_cc_test(
    name = "vectorize_test",
    srcs = ["vectorize_test.cc"],
    deps = [
        ":vectorize",
    ],
)

spu_cc_library(
    name = "parallel_utils",
    srcs = ["parallel_utils.cc"],
    hdrs = ["parallel_utils.h"],
    deps = [
        "@yacl//yacl/utils:parallel",
    ],
)

spu_cc_library(
    name = "logging",
    srcs = ["logging.cc"],
    hdrs = ["logging.h"],
    deps = [
        "//libspu/core:prelude",
        "@abseil-cpp//absl/strings",
        "@brpc//:butil",
        "@fmt",
        "@yacl//yacl/link:trace",
    ],
)

spu_cc_library(
    name = "bit_utils",
    srcs = ["bit_utils.cc"],
    hdrs = ["bit_utils.h"],
    deps = [
        "@abseil-cpp//absl/numeric:bits",
        "@yacl//yacl/base:int128",
        "@yacl//yacl/utils:platform_utils",
    ],
)

spu_cc_test(
    name = "bit_utils_test",
    srcs = ["bit_utils_test.cc"],
    deps = [
        ":bit_utils",
        "@yacl//yacl/base:int128",
    ],
)

spu_cc_binary(
    name = "bit_utils_bench",
    srcs = ["bit_utils_bench.cc"],
    linkopts = ["-lm"],
    deps = [
        ":bit_utils",
        "@google_benchmark//:benchmark_main",
    ],
)

spu_cc_library(
    name = "object",
    srcs = ["object.cc"],
    hdrs = ["object.h"],
    deps = [
        "//libspu/core:cexpr",
        "//libspu/core:type",
        "//libspu/core:value",
    ],
)

spu_cc_library(
    name = "cexpr",
    srcs = ["cexpr.cc"],
    hdrs = ["cexpr.h"],
    deps = [
        "//libspu/core:prelude",
    ],
)

spu_cc_test(
    name = "cexpr_test",
    srcs = ["cexpr_test.cc"],
    deps = [
        ":cexpr",
    ],
)

spu_cc_library(
    name = "context",
    srcs = ["context.cc"],
    hdrs = ["context.h"],
    deps = [
        "//libspu/core:config",
        "//libspu/core:object",
        "//libspu/core:trace",
        "@yacl//yacl/link",
    ],
)

spu_cc_library(
    name = "value",
    srcs = ["value.cc"],
    hdrs = ["value.h"],
    deps = [
        "//libspu/core:ndarray_ref",
        "//libspu/core:prelude",
        "//libspu/core:shape",
    ],
)

spu_cc_test(
    name = "value_test",
    srcs = ["value_test.cc"],
    deps = [
        "//libspu/core:value",
    ],
)

spu_cc_library(
    name = "half",
    hdrs = ["half.h"],
)
