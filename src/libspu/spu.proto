// Copyright 2021 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// This file defines data structures that used to access and control SPU.
// - IO access (data infeed & outfeed).
// - Runtime control (runtime config and setup)
//
// It does NOT define how to program on SPU, programming part is defined by
// [pphlo](spu/dialect/pphlo.td)

syntax = "proto3";

package spu.pb;

option java_package = "org.secretflow.spu";

// The SPU datatype
enum DataType {
  DT_INVALID = 0;

  DT_I1 = 1;    // 1bit integer (bool).
  DT_I8 = 2;    // int8
  DT_U8 = 3;    // uint8
  DT_I16 = 4;   // int16
  DT_U16 = 5;   // uint16
  DT_I32 = 6;   // int32
  DT_U32 = 7;   // uint32
  DT_I64 = 8;   // int64
  DT_U64 = 9;   // uint64
  DT_F16 = 10;  // half
  DT_F32 = 11;  // float
  DT_F64 = 12;  // double
}

// The visibility type.
//
// SPU is a secure evaluation runtime, but not all data are secret, some of them
// are publicly known to all parties, marking them as public will improve
// performance significantly.
enum Visibility {
  VIS_INVALID = 0;
  VIS_SECRET = 1;   // Invisible(unknown) for all or some of the parties.
  VIS_PUBLIC = 2;   // Visible(public) for all parties.
  VIS_PRIVATE = 3;  // Visible for only one party
}

// @exclude
// The shape information.
message ShapeProto {
  repeated int64 dims = 1;
}

// Plaintext type
//
// SPU runtime does not process with plaintext directly, plaintext type is
// mainly used for IO purposes, when converting a plaintext buffer to an SPU
// buffer, we have to let spu know which type the plaintext buffer is.
enum PtType {
  PT_INVALID = 0;  //
  PT_I8 = 1;       // int8_t
  PT_U8 = 2;       // uint8_t
  PT_I16 = 3;      // int16_t
  PT_U16 = 4;      // uint16_t
  PT_I32 = 5;      // int32_t
  PT_U32 = 6;      // uint32_t
  PT_I64 = 7;      // int64_t
  PT_U64 = 8;      // uint64_t
  PT_I128 = 9;     // int128_t
  PT_U128 = 10;    // uint128_t
  PT_I1 = 11;      // bool

  PT_F16 = 30;  // half
  PT_F32 = 31;  // float
  PT_F64 = 32;  // double

  PT_CF32 = 50;  // complex float
  PT_CF64 = 51;  // complex double
}

// A security parameter type.
//
// The secure evaluation is based on some algebraic structure (ring or field),
enum FieldType {
  FT_INVALID = 0;
  FM32 = 1;   // Ring 2^32
  FM64 = 2;   // Ring 2^64
  FM128 = 3;  // Ring 2^128
}

// The protocol kind.
enum ProtocolKind {
  // Invalid protocol.
  PROT_INVALID = 0;

  // The reference implementation in `ring^2k`, note: this 'protocol' only
  // behave-like a fixed point secure protocol without any security guarantee.
  // Hence, it should only be selected for debugging purposes.
  REF2K = 1;

  // A semi-honest multi-party protocol. This protocol requires a trusted third
  // party to generate the offline correlated randoms. Currently, SecretFlow by
  // default ships this protocol with a trusted first party. Hence, it should
  // only be used for debugging purposes.
  SEMI2K = 2;

  // A honest majority 3PC-protocol. SecretFlow provides the semi-honest
  // implementation without Yao.
  ABY3 = 3;

  // The famous [Cheetah](https://eprint.iacr.org/2022/207) protocol, a very
  // fast 2PC protocol.
  CHEETAH = 4;

  // A semi-honest 3PC-protocol for Neural Network, P2 as the helper,
  // (https://eprint.iacr.org/2018/442)
  SECURENN = 5;
}

message ValueMetaProto {
  // The data type.
  DataType data_type = 1;
  bool is_complex = 2;

  // The data visibility.
  Visibility visibility = 3;

  // The shape of the value.
  ShapeProto shape = 4;

  // The storage type, defined by the underline evaluation engine.
  // i.e. `aby3.AShr<FM64>` means an aby3 arithmetic share in FM64.
  // usually, the application does not care about this attribute.
  string storage_type = 5;
}

// The spu Value proto, used for spu value serialization.
message ValueChunkProto {
  // chunk info
  uint64 total_bytes = 1;
  uint64 chunk_offset = 2;
  // chunk bytes
  bytes content = 3;
}

//////////////////////////////////////////////////////////////////////////
// Runtime configuration
//////////////////////////////////////////////////////////////////////////

// The SPU runtime configuration.
message RuntimeConfig {
  //

  ///////////////////////////////////////
  // Basic
  ///////////////////////////////////////

  // The protocol kind.
  ProtocolKind protocol = 1;

  // The field type.
  FieldType field = 2;

  // Number of fraction bits of fixed-point number.
  // 0(default) indicates implementation defined.
  int64 fxp_fraction_bits = 3;

  // Max number of cores
  int32 max_concurrency = 4;

  ///////////////////////////////////////
  // Advanced
  ///////////////////////////////////////

  // @exclude
  // Runtime related, reserved for [10, 50)

  // When enabled, runtime prints verbose info of the call stack, debug purpose
  // only.
  bool enable_action_trace = 10;

  // When enabled, runtime checks runtime type infos against the
  // compile-time ones, exceptions are raised if mismatches happen. Note:
  // Runtime outputs prefer runtime type infos even when flag is on.
  bool enable_type_checker = 11;

  // When enabled, runtime prints executed pphlo list, debug purpose only.
  bool enable_pphlo_trace = 12;

  // When enabled, runtime dumps executed executables in the dump_dir, debug
  // purpose only.
  bool enable_runtime_snapshot = 13;
  string snapshot_dump_dir = 14;

  // When enabled, runtime records detailed pphlo timing data, debug purpose
  // only.
  // WARNING: the `send bytes` information is only accurate when
  // `experimental_enable_inter_op_par` and `experimental_enable_intra_op_par`
  // options are disabled.
  bool enable_pphlo_profile = 15;

  // When enabled, runtime records detailed hal timing data, debug purpose only.
  // WARNING: the `send bytes` information is only accurate when
  // `experimental_enable_inter_op_par` and `experimental_enable_intra_op_par`
  // options are disabled.
  bool enable_hal_profile = 16;

  reserved 17, 18;

  // The public random variable generated by the runtime, the concrete prg
  // function is implementation defined.
  // Note: this seed only applies to `public variable` only, it has nothing
  // to do with security.
  uint64 public_random_seed = 19;

  // max chunk size for Value::toProto
  // default: 128 * 1024 * 1024
  uint64 share_max_chunk_size = 20;

  enum SortMethod {
    SORT_DEFAULT = 0;  // Implementation defined.
    SORT_RADIX = 1;    // The radix sort (stable sort, need efficient shuffle).
    SORT_QUICK = 2;    // The quick sort (unstable, need efficient shuffle).
    SORT_NETWORK = 3;  // The odd-even sorting network (unstable, most general).
  }

  // SPU supports multiple sorting algorithms.
  //  -for 2pc, only sorting network is supported.
  //  -for 2.5pc or 3pc, all these algorithms are supported.
  //  -for stable sort, only radix sort is supported.
  SortMethod sort_method = 21;

  // threshold for quick sort, when the size of the array is less than this
  // value, use merge sort instead
  int64 quick_sort_threshold = 22;

  // @exclude
  // Fixed-point arithmetic related, reserved for [50, 100)

  // The iterations use in f_div with Goldschmidt method.
  // 0(default) indicates implementation defined.
  int64 fxp_div_goldschmidt_iters = 50;

  // The exponential approximation method.
  enum ExpMode {
    EXP_DEFAULT = 0;  // Implementation defined.
    EXP_PADE = 1;     // The pade approximation.
    EXP_TAYLOR = 2;   // Taylor series approximation.
    EXP_PRIME = 3;    // exp prime only available for some implementations
  }

  // The exponent approximation method.
  ExpMode fxp_exp_mode = 51;

  // Number of iterations of `exp` approximation, 0(default) indicates impl
  // defined.
  int64 fxp_exp_iters = 52;

  // The logarithm approximation method.
  enum LogMode {
    LOG_DEFAULT = 0;  // Implementation defined.
    LOG_PADE = 1;     // The pade approximation.
    LOG_NEWTON = 2;   // The newton approximation.
    LOG_MINMAX = 3;   // The minmax approximation.
  }

  // The logarithm approximation method.
  LogMode fxp_log_mode = 53;

  // Number of iterations of `log` approximation, 0(default) indicates
  // impl-defined.
  int64 fxp_log_iters = 54;

  // Number of orders of `log` approximation, 0(default) indicates impl defined.
  int64 fxp_log_orders = 55;

  // The sigmoid approximation method.
  enum SigmoidMode {
    // Implementation defined.
    SIGMOID_DEFAULT = 0;
    // Minmax approximation one order.
    // f(x) = 0.5 + 0.125 * x
    SIGMOID_MM1 = 1;
    // Piece-wise simulation.
    // f(x) = 0.5 + 0.125x if -4 <= x <= 4
    //        1            if       x > 4
    //        0            if  -4 > x
    SIGMOID_SEG3 = 2;
    // The real definition, which depends on exp's accuracy.
    // f(x) = 1 / (1 + exp(-x))
    SIGMOID_REAL = 3;
  }

  // The sigmoid function approximation model.
  SigmoidMode sigmoid_mode = 56;

  // Enable a simpler rsqrt approximation
  bool enable_lower_accuracy_rsqrt = 57;

  // Sine/Cosine approximation iterations
  int64 sine_cosine_iters = 58;

  /// - MPC protocol related definitions.

  enum BeaverType {
    // Assume first party (rank0) as trusted party to generate beaver triple.
    // WARNING: It is NOT SAFE and SHOULD NOT BE used in production.
    TrustedFirstParty = 0;
    // Generate beaver triple through an additional trusted third party.
    TrustedThirdParty = 1;
    // Generate beaver triple through multi-party.
    MultiParty = 2;
  }
  // beaver config, works for semi2k and spdz2k for now.
  BeaverType beaver_type = 70;

  // TrustedThirdParty configs.
  TTPBeaverConfig ttp_beaver_config = 71;

  // Cheetah 2PC configs.
  CheetahConfig cheetah_2pc_config = 72;

  // For protocol like SecureML, the most significant bit may have error with
  // low probability, which lead to huge calculation error.
  bool trunc_allow_msb_error = 73;

  /// System related configurations start.

  // Experimental: DO NOT USE
  bool experimental_disable_mmul_split = 100;
  // Inter op parallel, aka, DAG level parallel.
  bool experimental_enable_inter_op_par = 101;
  // Intra op parallel, aka, hal/mpc level parallel.
  bool experimental_enable_intra_op_par = 102;
  // Disable kernel level vectorization.
  bool experimental_disable_vectorization = 103;
  // Inter op concurrency.
  uint64 experimental_inter_op_concurrency = 104;
  // Enable use of private type
  bool experimental_enable_colocated_optimization = 105;

  // enable experimental exp prime method
  bool experimental_enable_exp_prime = 106;

  // The offset parameter for exp prime methods.
  // control the valid range of exp prime method.
  // valid range is:
  // ((47 - offset - 2fxp)/log_2(e), (125 - 2fxp - offset)/log_2(e))
  // clamp to value would be
  //                lower bound: (48 - offset - 2fxp)/log_2(e)
  //                higher bound: (124 - 2fxp - offset)/log_2(e)
  // default offset is 13, 0 offset is not supported.
  uint32 experimental_exp_prime_offset = 107;
  // whether to apply the clamping lower bound
  // default to enable it
  bool experimental_exp_prime_disable_lower_bound = 108;
  // whether to apply the clamping upper bound
  // default to disable it
  bool experimental_exp_prime_enable_upper_bound = 109;
}

message ClientSSLConfig {
  // Certificate in PEM format, supported both file path and raw string
  string certificate = 1;
  // Private key in PEM format, supported both file path and raw string based on
  // prefix
  string private_key = 2;
  // The trusted CA file to verify the peer's certificate
  // If empty, use the system default CA files
  string ca_file_path = 3;
  // Maximum depth of the certificate chain for verification
  // If 0, turn off the verification
  int32 verify_depth = 4;
}

message TTPBeaverConfig {
  // TrustedThirdParty beaver server's remote ip:port or load-balance uri.
  string server_host = 1;
  // which rank do adjust rpc call, usually choose the rank closer to the
  // server.
  int32 adjust_rank = 2;

  // asym_crypto_schema: support ["SM2"]
  // Will support 25519 in the future, after yacl supported it.
  string asym_crypto_schema = 3;
  // Server's public key in PEM format
  string server_public_key = 4;

  // Transport protocol, support ["http", "h2"]
  string transport_protocol = 5;

  // Configurations related to SSL
  ClientSSLConfig ssl_config = 6;
}

enum CheetahOtKind {
  YACL_Ferret = 0;
  YACL_Softspoken = 1;
  EMP_Ferret = 2;
}

message CheetahConfig {
  // disable the ciphertext packing for matmul
  bool disable_matmul_pack = 1;
  // allow least significant bits error for point-wise mul
  bool enable_mul_lsb_error = 2;
  // Setup for cheetah ot
  CheetahOtKind ot_kind = 3;
}
//////////////////////////////////////////////////////////////////////////
// Compiler relate definition
//////////////////////////////////////////////////////////////////////////
enum SourceIRType {
  XLA = 0;
  STABLEHLO = 1;
}

message CompilationSource {
  // Input IR type
  SourceIRType ir_type = 1;

  // IR
  bytes ir_txt = 2;

  // Input visibilities
  repeated Visibility input_visibility = 3;
}

enum XLAPrettyPrintKind {
  TEXT = 0;
  DOT = 1;
  HTML = 2;
}

message CompilerOptions {
  // Pretty print
  bool enable_pretty_print = 1;
  string pretty_print_dump_dir = 2;
  XLAPrettyPrintKind xla_pp_kind = 3;

  // Disable sqrt(x) + eps to sqrt(x+eps) rewrite
  bool disable_sqrt_plus_epsilon_rewrite = 10;

  // Disable x/sqrt(y) to x*rsqrt(y) rewrite
  bool disable_div_sqrt_rewrite = 11;

  // Disable reduce truncation optimization
  bool disable_reduce_truncation_optimization = 12;

  // Disable maxpooling optimization
  bool disable_maxpooling_optimization = 13;

  // Disallow mix type operations
  bool disallow_mix_types_opts = 14;

  // Disable SelectOp optimization
  bool disable_select_optimization = 15;

  // Enable optimize x/bcast(y) -> x * bcast(1/y)
  bool enable_optimize_denominator_with_broadcast = 16;

  // Disable deallocation insertion pass
  bool disable_deallocation_insertion = 17;

  // Disable sort->topk rewrite when only partial sort is required
  bool disable_partial_sort_optimization = 28;
}

// The executable format accepted by SPU runtime.
//
// - Inputs should be prepared before running executable.
// - Output is maintained after execution, and can be fetched by output name.
//
// ```python
//   rt = spu.Runtime(...)            # create an spu runtime.
//   rt.set_var('x', ...)             # set variable to the runtime.
//   exe = spu.ExecutableProto(       # prepare the executable.
//           name = 'balabala',
//           input_names = ['x'],
//           output_names = ['y'],
//           code = ...)
//   rt.run(exe)                      # run the executable.
//   y = rt.get_var('y')              # get the executable from spu runtime.
// ```
message ExecutableProto {
  // The name of the executable.
  string name = 1;

  // The input names.
  repeated string input_names = 2;

  // The output names.
  repeated string output_names = 4;

  // The bytecode of the program, with format IR_MLIR_SPU.
  bytes code = 6;
}
