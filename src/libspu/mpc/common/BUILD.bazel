# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "pv2k",
    srcs = ["pv2k.cc"],
    hdrs = ["pv2k.h"],
    deps = [
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/utils:ring_ops",
        "@magic_enum",
    ],
)

spu_cc_test(
    name = "pv2k_test",
    srcs = ["pv2k_test.cc"],
    deps = [
        ":pv2k",
    ],
)

spu_cc_library(
    name = "communicator",
    srcs = ["communicator.cc"],
    hdrs = ["communicator.h"],
    deps = [
        "//libspu/core:object",
        "//libspu/mpc/utils:gfmp_ops",
        "//libspu/mpc/utils:ring_ops",
        "@yacl//yacl/link:context",
        "@yacl//yacl/link/algorithm:allgather",
        "@yacl//yacl/link/algorithm:broadcast",
        "@yacl//yacl/link/algorithm:gather",
    ],
)

spu_cc_test(
    name = "communicator_test",
    srcs = ["communicator_test.cc"],
    deps = [
        ":communicator",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_library(
    name = "prg_state",
    srcs = ["prg_state.cc"],
    hdrs = ["prg_state.h"],
    deps = [
        "//libspu/core:object",
        "//libspu/mpc/utils:permute",
        "@yacl//yacl/crypto/rand",
        "@yacl//yacl/crypto/tools:prg",
        "@yacl//yacl/link:context",
        "@yacl//yacl/link/algorithm:allgather",
    ],
)

spu_cc_test(
    name = "prg_state_test",
    srcs = ["prg_state_test.cc"],
    deps = [
        ":prg_state",
        "//libspu/mpc/utils:simulate",
        "@yacl//yacl/link/algorithm:barrier",
    ],
)

spu_cc_library(
    name = "prg_tensor",
    hdrs = ["prg_tensor.h"],
    deps = [
        "//libspu/core:ndarray_ref",
        "//libspu/mpc/utils:gfmp_ops",
        "//libspu/mpc/utils:ring_ops",
        "@yacl//yacl/crypto/tools:prg",
    ],
)
