# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "ferret_ot_interface",
    hdrs = ["ferret_ot_interface.h"],
)

spu_cc_library(
    name = "ot_util",
    srcs = ["ot_util.cc"],
    hdrs = ["ot_util.h"],
    deps = [
        "//libspu/core:ndarray_ref",
        "//libspu/core:prelude",
        "//libspu/mpc/common:communicator",
        "@abseil-cpp//absl/types:span",
        "@yacl//yacl/base:int128",
    ] + select({
        "@platforms//cpu:aarch64": [
            "@sse2neon",
        ],
        "//conditions:default": [],
    }),
)

spu_cc_library(
    name = "ot",
    srcs = ["basic_ot_prot.cc"],
    hdrs = ["basic_ot_prot.h"],
    deps = [
        ":ot_util",
        "//libspu/mpc/cheetah:type",
        "//libspu/mpc/cheetah/ot/emp:ferret",
        "//libspu/mpc/cheetah/ot/yacl:ferret",
        "//libspu/mpc/common:communicator",
        "@yacl//yacl/base:int128",
        "@yacl//yacl/link",
    ],
)

spu_cc_test(
    name = "basic_ot_prot_test",
    size = "large",
    srcs = ["basic_ot_prot_test.cc"],
    tags = [
        "exclusive-if-local",
    ],
    deps = [
        ":ot",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_test(
    name = "ot_util_test",
    srcs = ["ot_util_test.cc"],
    deps = [
        ":ot_util",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/utils:ring_ops",
        "//libspu/mpc/utils:simulate",
    ],
)
