# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "cheetah_nonlinear",
    deps = [
        ":compare_prot",
        ":equal_prot",
        ":truncate_prot",
    ],
)

spu_cc_library(
    name = "compare_prot",
    srcs = ["compare_prot.cc"],
    hdrs = ["compare_prot.h"],
    deps = [
        "//libspu/mpc/cheetah/ot",
        "@yacl//yacl/link",
    ],
)

spu_cc_library(
    name = "equal_prot",
    srcs = ["equal_prot.cc"],
    hdrs = ["equal_prot.h"],
    deps = [
        "//libspu/mpc/cheetah/ot",
        "@yacl//yacl/link",
    ],
)

spu_cc_library(
    name = "truncate_prot",
    srcs = ["truncate_prot.cc"],
    hdrs = ["truncate_prot.h"],
    deps = [
        ":compare_prot",
    ],
)

spu_cc_test(
    name = "compare_prot_test",
    srcs = ["compare_prot_test.cc"],
    tags = [
        "exclusive-if-local",
    ],
    deps = [
        ":compare_prot",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_test(
    name = "truncate_prot_test",
    srcs = ["truncate_prot_test.cc"],
    deps = [
        ":truncate_prot",
        "//libspu/mpc/utils:simulate",
    ],
)

spu_cc_test(
    name = "equal_prot_test",
    srcs = ["equal_prot_test.cc"],
    deps = [
        ":equal_prot",
        "//libspu/mpc/utils:simulate",
    ],
)
