# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@yacl//bazel:yacl.bzl", "AES_COPT_FLAGS")
load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "cheetah",
    deps = [
        ":io",
        ":protocol",
    ],
)

spu_cc_library(
    name = "state",
    srcs = ["state.cc"],
    hdrs = ["state.h"],
    copts = AES_COPT_FLAGS,
    deps = [
        "//libspu/mpc/cheetah/arith:cheetah_arith",
        "//libspu/mpc/cheetah/nonlinear:cheetah_nonlinear",
        "//libspu/mpc/cheetah/ot",
        "//libspu/mpc/cheetah/rlwe:cheetah_rlwe",
    ],
)

spu_cc_library(
    name = "boolean",
    srcs = [
        "boolean.cc",
        "boolean_semi2k.cc",
    ],
    hdrs = ["boolean.h"],
    copts = AES_COPT_FLAGS,
    deps = [
        ":state",
        ":type",
    ],
)

spu_cc_library(
    name = "conversion",
    srcs = ["conversion.cc"],
    hdrs = ["conversion.h"],
    copts = AES_COPT_FLAGS,
    deps = [
        ":state",
        ":type",
        "//libspu/core:vectorize",
        "//libspu/mpc:ab_api",
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
    ],
)

spu_cc_library(
    name = "arithmetic",
    srcs = [
        "arithmetic.cc",
        "arithmetic_semi2k.cc",
    ],
    hdrs = ["arithmetic.h"],
    copts = AES_COPT_FLAGS,
    deps = [
        ":state",
        ":type",
    ],
)

spu_cc_library(
    name = "protocol",
    srcs = ["protocol.cc"],
    hdrs = ["protocol.h"],
    copts = AES_COPT_FLAGS,
    deps = [
        ":arithmetic",
        ":boolean",
        ":conversion",
        ":permute",
        ":state",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/common:pv2k",
        "//libspu/mpc/standard_shape:protocol",
    ],
)

spu_cc_test(
    name = "protocol_api_test",
    size = "large",
    timeout = "moderate",
    srcs = ["protocol_api_test.cc"],
    tags = [
        "exclusive-if-local",
    ],
    deps = [
        ":protocol",
        "//libspu/mpc:api_test",
    ],
)

spu_cc_test(
    name = "protocol_ab_test",
    size = "large",
    timeout = "moderate",
    srcs = ["protocol_ab_test.cc"],
    tags = [
        "exclusive-if-local",
    ],
    deps = [
        ":protocol",
        "//libspu/mpc:ab_api_test",
    ],
)

spu_cc_library(
    name = "io",
    srcs = ["io.cc"],
    hdrs = ["io.h"],
    deps = [
        ":type",
        "//libspu/mpc:io_interface",
        "//libspu/mpc/utils:ring_ops",
    ],
)

spu_cc_library(
    name = "type",
    srcs = ["type.cc"],
    hdrs = ["type.h"],
    deps = [
        "//libspu/core:type",
        "//libspu/mpc/common:pv2k",
    ],
)

spu_cc_library(
    name = "permute",
    srcs = ["permute.cc"],
    hdrs = ["permute.h"],
    deps = [
        ":type",
        "//libspu/mpc:ab_api",
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/common:pv2k",
        "//libspu/mpc/utils:permute",
        "//libspu/mpc/utils:ring_ops",
        "//libspu/mpc/utils:waksman_net",
    ],
)
