# Copyright 2025 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "ppmlac_factory",
    srcs = ["ppmlac_factory.cc"],
    hdrs = ["ppmlac_factory.h"],
    deps = [
        ":ppmlac_receiver",
        ":ppmlac_sender",
    ],
)

spu_cc_library(
    name = "ppmlac_receiver",
    srcs = ["ppmlac_receiver.cc"],
    hdrs = ["ppmlac_receiver.h"],
    deps = [
        ":ppmlac",
        ":utils",
        "//libspu/core:prelude",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/semi2k/ppmlac/trusted_chip:trusted_service_cc_proto",
        "//libspu/mpc/semi2k",
        "//libspu/mpc/semi2k/ppmlac/trusted_chip:stream_reader",
    ],
)

spu_cc_library(
    name = "ppmlac_sender",
    srcs = ["ppmlac_sender.cc"],
    hdrs = ["ppmlac_sender.h"],
    deps = [
        ":ppmlac",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/semi2k/ppmlac/trusted_chip",
    ],
)

spu_cc_library(
    name = "ppmlac",
    hdrs = ["ppmlac.h"],
    deps = [
        "//libspu/core:context",
        "//libspu/core:ndarray_ref",
        "@yacl//yacl/link",
    ],
)

spu_cc_library(
    name = "utils",
    hdrs = ["utils.h"],
    srcs = ["utils.cc"],
    deps = [
        ":stream_meta",
        "//libspu/core:prelude",
        "@brpc",
        "@yacl//yacl/base:buffer",
    ],
)

spu_cc_library(
    name = "stream_meta",
    hdrs = ["stream_meta.h"],
)

spu_cc_test(
    name = "ppmlac_test",
    srcs = ["ppmlac_test.cc"],
    deps = [
        ":ppmlac_factory",
        "//libspu/mpc/semi2k/ppmlac/trusted_chip:trusted_server",
        "//libspu/mpc/utils:simulate",
    ],
)
