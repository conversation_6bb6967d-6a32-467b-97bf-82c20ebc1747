// Copyright 2025 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package spu.mpc.semi2k.ppmlac;

option cc_generic_services = true;

enum ErrorCode {
  OK = 0;
  StreamAcceptError = 1;
}

// Field type enum matching the core SPU FieldType
enum FieldType {
  FT_INVALID = 0;
  FM32 = 1;   // Ring 2^32
  FM64 = 2;   // Ring 2^64
  FM128 = 3;  // Ring 2^128
}

message GetPubKeyRequest {}

message GetPubKeyResponse {
  string asym_crypto_schema = 1;
  bytes pub_key = 2;
}

message ExRandNumRequest {
  int64 rank = 1;  // rank of the sender
  bytes enc_pub_key = 2;  // encrypted public key generated by the sender
  bytes enc_rand_num = 3;  // encrypted random number generated by the sender
}

message ExRandNumResponse {
  bytes enc_rand_num = 3;  // encrypted random number generated by the receiver
}

message MulRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // Input
  // x - a
  // y - b

  // output
  // z1 = x * y - q1
}

message SquareRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // Input
  // x - a

  // output
  // z1 = x * x - q1
}

message DotRequest {
  FieldType field_type = 1;

  // x's shape: (M, K), y's shape: (K, N)
  uint64 M = 2;
  uint64 N = 3;
  uint64 K = 4;

  // Input
  // x - a
  // y - b

  // output
  // z1 = matmul(x, y) - q1
}

message AndRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // output
  // z1 = x & y ^ q1
}

message TruncRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // how many bits need to truncate.
  uint32 bits = 3;

  // Input
  // x - a

  // output
  // z1 = x >> bits - q1
}

message TruncPrRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // how many bits need to truncate.
  uint32 bits = 3;

  // Input
  // x - a

  // output
  // z1 = (x << 1) >> (bits + 1) - q1
}

message RandBitRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // Input
  // None

  // output
  // z1 = random 0/1 array - q1
}

message EqzRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  // Input
  // x - a

  // output
  // z1 = (x == 0) ^ q1
}

message PermRequest {
  FieldType field_type = 1;

  int64 num_elements = 2;

  uint64 perm_rank = 3;

  // output
  // z1 = Inv(x, perm) - q1
}

message Response {
  ErrorCode code = 1;
  string message = 2;
}

service TrustedService {
  // Get public key
  rpc GetPubKey(GetPubKeyRequest) returns (GetPubKeyResponse);

  // Exchange random number
  rpc ExRandNum(ExRandNumRequest) returns (ExRandNumResponse);

  rpc Mul(MulRequest) returns (Response);

  rpc Square(SquareRequest) returns (Response);

  rpc Dot(DotRequest) returns (Response);

  rpc And(AndRequest) returns (Response);

  rpc Trunc(TruncRequest) returns (Response);

  rpc TruncPr(TruncPrRequest) returns (Response);

  rpc RandBit(RandBitRequest) returns (Response);

  rpc Eqz(EqzRequest) returns (Response);

  rpc Perm(PermRequest) returns (Response);
}
