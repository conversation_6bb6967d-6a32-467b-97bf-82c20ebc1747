// Copyright 2025 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <unordered_map>

#include "yacl/base/buffer.h"
#include "yacl/base/byte_container_view.h"
#include "yacl/base/int128.h"

#include "libspu/mpc/semi2k/ppmlac/trusted_chip/prng.h"

namespace spu::mpc::semi2k::ppmlac {

class TrustedChip {
 public:
  TrustedChip(const std::string& asym_crypto_schema,
              yacl::ByteContainerView public_key,
              yacl::ByteContainerView private_key);

  std::string GetAsymCryptoSchema() const { return asym_crypto_schema_; }

  yacl::Buffer GetPubKey() const { return public_key_; }

  // Encrypt the public key of the trusted chip using the provided pub_key
  // parameter
  yacl::Buffer EncryptPubKey(yacl::ByteContainerView pub_key) const;

  // Encrypt the random number generated by the trusted chip using the provided
  // pub_key parameter
  yacl::Buffer EncryptRandNum(yacl::ByteContainerView pub_key) const;

  // Decrypt the public key of the trusted chip using the private key of the
  // trusted chip
  yacl::Buffer DecryptPubKey(yacl::ByteContainerView enc_pk) const;

  // Decrypt the random number generated by the trusted chip using the private
  // key of the trusted chip
  uint128_t DecryptRandNum(yacl::ByteContainerView enc_rn) const;

  // Setup PRNG for the given rank using the encrypted random number
  void SetupPRNG(size_t rank, yacl::ByteContainerView enc_rn);

  NdArrayRef GenRnd(size_t rank, FieldType field, const Shape& shape) {
    return prngs_.at(rank).FillRing(field, shape);  // TODO: add lock
  }

  std::vector<NdArrayRef> GenRnd(FieldType field, const Shape& shape);

 private:
  std::string asym_crypto_schema_;
  yacl::Buffer public_key_;
  yacl::Buffer private_key_;
  uint128_t rand_num_;  // random number generated by the trusted chip
  std::unordered_map<size_t, PRNG> prngs_;  // rank -> prng
};

}  // namespace spu::mpc::semi2k::ppmlac
