# Copyright 2023 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//bazel:spu.bzl", "spu_cc_binary", "spu_cc_library")

package(default_visibility = ["//visibility:public"])

cc_proto_library(
    name = "service_cc_proto",
    deps = [":service_proto"],
)

proto_library(
    name = "service_proto",
    srcs = ["service.proto"],
)

cc_proto_library(
    name = "config_cc_proto",
    deps = [":config_proto"],
)

proto_library(
    name = "config_proto",
    srcs = ["config.proto"],
)

spu_cc_library(
    name = "beaver_server",
    srcs = ["beaver_server.cc"],
    hdrs = ["beaver_server.h"],
    deps = [
        ":service_cc_proto",
        "//libspu/mpc/semi2k/beaver/beaver_impl/trusted_party",
        "@brpc",
        "@yacl//yacl/crypto/pke:sm2_enc",
    ],
)

spu_cc_binary(
    name = "beaver_server_main",
    srcs = ["beaver_server_main.cc"],
    deps = [
        ":beaver_server",
        ":config_cc_proto",
        "//libspu/core:logging",
    ],
)
