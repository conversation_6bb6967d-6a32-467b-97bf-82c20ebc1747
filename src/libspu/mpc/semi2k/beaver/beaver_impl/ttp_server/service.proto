// Copyright 2023 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package spu.mpc.semi2k.beaver.ttp_server;

option cc_generic_services = true;

enum ErrorCode {
  OK = 0;
  OpAdjustError = 1;
  SeedDecryptError = 2;
}

// The type of element in the field.
// Match the enum in libspu/mpc/common/prg_tensor.h
enum ElType {
  UNSPECIFIED = 0;
  RING = 1;
  GFMP = 2;
}
// PRG generated buffer metainfo.
// BeaverService replay PRG to generate same buffer using each party's prg_seed
// encrypted by server's public key. PrgBufferMeta represent {world_size}
// arithmetic/boolean shares for one random array.
message PrgBufferMeta {
  uint64 prg_count = 1;
  // buffer len in bytes
  uint64 buffer_len = 2;
  repeated bytes encrypted_seeds = 3;
  bool transpose = 4;
}

message PrgRandPermMeta {
  uint64 prg_count = 1;
  // permutation size
  int64 size = 2;
  bytes encrypted_seeds = 3;
}

// TTP Beaver service for semi2k only.
service BeaverService {
  // V1 adjust ops
  rpc AdjustMul(AdjustMulRequest) returns (AdjustResponse);

  rpc AdjustMulPriv(AdjustMulPrivRequest) returns (AdjustResponse);

  rpc AdjustSquare(AdjustSquareRequest) returns (AdjustResponse);

  rpc AdjustDot(AdjustDotRequest) returns (AdjustResponse);

  rpc AdjustAnd(AdjustAndRequest) returns (AdjustResponse);

  rpc AdjustTrunc(AdjustTruncRequest) returns (AdjustResponse);

  rpc AdjustTruncPr(AdjustTruncPrRequest) returns (AdjustResponse);

  rpc AdjustRandBit(AdjustRandBitRequest) returns (AdjustResponse);

  rpc AdjustEqz(AdjustEqzRequest) returns (AdjustResponse);

  rpc AdjustPerm(AdjustPermRequest) returns (AdjustResponse);
}

message AdjustMulRequest {
  // input three prg buffer
  // reconstruct all parties' share get: ra / rb / rc
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // output
  // adjust_c = ra * rb - rc
  // make
  // ra * rb = (adjust_c + rc)

  // element type supported: "GFMP", "RING"
  ElType element_type = 3;
  // if element type is "GFMP" then all ring ops will be changed to gfmp
}

message AdjustMulPrivRequest {
  // input 2 prg buffer
  // first is a or b [one party holds a slice, another b slice]
  // second is c
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // output
  // adjust_c = a * b - rc
  // make
  // a * b = (adjust_c + rc)

  // element type supported: "GFMP", "RING"
  ElType element_type = 3;
  // if element type is "GFMP" then all ring ops will be changed to gfmp
}

message AdjustSquareRequest {
  // input two prg buffer
  // reconstruct all parties' share get: ra / rb
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // output
  // adjust_b = ra * ra - rb
  // make
  // ra * ra = (adjust_b + rb)
}

message AdjustDotRequest {
  // input three prg buffer
  // reconstruct all parties' share get: ra / rb / rc
  repeated PrgBufferMeta prg_inputs = 1;
  // if buffer need reshaped by transpose
  repeated bool transpose_inputs = 2;
  // What field size should be used to interpret buffer content
  uint32 field_size = 3;
  // ra's shape: (M, K), rb's shape: (K, N), rc's shape: (M, N)
  uint64 M = 4;
  uint64 N = 5;
  uint64 K = 6;
  // output
  // adjust_c = matmul(ra, rb) - rc
  // make
  // matmul(ra, rb) = (adjust_c + rc)
}

message AdjustAndRequest {
  // input three prg buffer
  // reconstruct all parties' share get: ra / rb / rc
  repeated PrgBufferMeta prg_inputs = 1;
  // output
  // adjust_c = (ra & rb) ^ rc
  // make
  // ra & rb = (adjust_c ^ rc)
}

message AdjustTruncRequest {
  // input two prg buffer
  // reconstruct all parties' share get: ra / rb
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // how many bits need to truncate.
  uint32 bits = 3;
  // output
  // adjust_b = (ra >> bits) - rb
  // makes
  // ra >> bits = (adjust_b + rb)
}

message AdjustTruncPrRequest {
  // input two prg buffer
  // reconstruct all parties' share get: ra / rb / rc
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // how many bits need to truncate.
  uint32 bits = 3;
  // output
  // adjust1 = ((ra << 1) >> (bits + 1)) - rb
  // adjust2 = msb(ra) - rc
  // make
  // (adjust1 + rb) = (ra mod 2^(ring_size-1)) >> bits
  // (adjust2 + rc) = msb(ra)
}

message AdjustRandBitRequest {
  // input one prg buffer
  // reconstruct all parties' share get: ra
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // output
  // adjust_a = server generated random 0/1 array - rb
  // make
  // (adjust_a + ra) = random 0/1 array
}

message AdjustEqzRequest {
  // input two prg buffer
  // reconstruct all parties' share get: ra / rb
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // output
  // adjust_b = rb
  // make
  // ra(in a share) = rb(in b share)
}

message AdjustPermRequest {
  // input two prg buffer
  // reconstruct all parties' share get: ra / rb
  repeated PrgBufferMeta prg_inputs = 1;
  // What field size should be used to interpret buffer content
  uint32 field_size = 2;
  // Rand permutation
  PrgRandPermMeta perm = 3;
  // output
  // adjust_b = (apply inverse permutation perm to ra) - rb
  // make
  // (adjust_b + rb) = apply inverse permutation perm to ra
}

message AdjustResponse {}
