# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "beaver_tfp",
    srcs = ["beaver_tfp.cc"],
    hdrs = ["beaver_tfp.h"],
    deps = [
        "//libspu/mpc/common:prg_tensor",
        "//libspu/mpc/semi2k/beaver:beaver_interface",
        "//libspu/mpc/semi2k/beaver/beaver_impl/trusted_party",
        "//libspu/mpc/utils:gfmp_ops",
        "//libspu/mpc/utils:permute",
        "//libspu/mpc/utils:ring_ops",
        "@seal",
        "@yacl//yacl/link",
        "@yacl//yacl/utils:parallel",
    ],
)

spu_cc_test(
    name = "beaver_test",
    srcs = ["beaver_test.cc"],
    deps = [
        ":beaver_tfp",
        ":beaver_ttp",
        "//libspu/core:xt_helper",
        "//libspu/mpc/semi2k/beaver/beaver_impl/ttp_server:beaver_server",
        "//libspu/mpc/utils:gfmp",
        "//libspu/mpc/utils:permute",
        "//libspu/mpc/utils:simulate",
        "@googletest//:gtest",
    ],
)

spu_cc_library(
    name = "beaver_ttp",
    srcs = ["beaver_ttp.cc"],
    hdrs = ["beaver_ttp.h"],
    deps = [
        "//libspu/mpc/common:prg_tensor",
        "//libspu/mpc/semi2k/beaver:beaver_interface",
        "//libspu/mpc/semi2k/beaver/beaver_impl/ttp_server:service_cc_proto",
        "//libspu/mpc/utils:gfmp_ops",
        "//libspu/mpc/utils:permute",
        "//libspu/mpc/utils:ring_ops",
        "@yacl//yacl/crypto/pke:sm2_enc",
        "@yacl//yacl/link",
        "@yacl//yacl/link/algorithm:barrier",
        "@yacl//yacl/utils:parallel",
        "@yacl//yacl/utils:serialize",
    ],
)
