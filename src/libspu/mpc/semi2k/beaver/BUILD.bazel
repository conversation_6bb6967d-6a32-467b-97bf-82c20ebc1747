# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "beaver_cache",
    srcs = ["beaver_cache.cc"],
    hdrs = ["beaver_cache.h"],
    deps = [
        ":beaver_interface",
        "//libspu/core:ndarray_ref",
        "//libspu/core:shape",
        "//libspu/mpc/common:prg_tensor",
        "@leveldb",
    ],
)

spu_cc_library(
    name = "beaver_interface",
    hdrs = ["beaver_interface.h"],
    deps = [
        "//libspu/core:ndarray_ref",
    ],
)
