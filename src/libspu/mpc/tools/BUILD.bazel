# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("@rules_cc//cc:defs.bzl", "cc_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")
load("//bazel:spu.bzl", "spu_cc_binary")

package(
    default_visibility = ["//visibility:public"],
)

proto_library(
    name = "complexity_proto",
    srcs = ["complexity.proto"],
)

cc_proto_library(
    name = "complexity_cc_proto",
    deps = [":complexity_proto"],
)

spu_cc_binary(
    name = "complexity",
    srcs = ["complexity.cc"],
    deps = [
        ":complexity_cc_proto",
        "//libspu/core:context",
        "//libspu/mpc:api",
        "//libspu/mpc:factory",
        "//libspu/mpc:kernel",
        "//libspu/mpc/utils:simulate",
        "@llvm-project//llvm:Support",
    ],
)

spu_cc_binary(
    name = "benchmark",
    srcs = [
        "benchmark.cc",
        "benchmark.h",
    ],
    deps = [
        "//libspu/core:context",
        "//libspu/mpc:api",
        "//libspu/mpc/aby3",
        "//libspu/mpc/cheetah",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/semi2k",
        "//libspu/mpc/utils:simulate",
        "@abseil-cpp//absl/strings",
        "@fmt",
        "@google_benchmark//:benchmark",
        "@llvm-project//llvm:Support",
        "@yacl//yacl/link:context",
    ],
)
