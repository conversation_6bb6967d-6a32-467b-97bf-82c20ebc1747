# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "ref2k",
    srcs = ["ref2k.cc"],
    hdrs = ["ref2k.h"],
    deps = [
        "//libspu/core:context",
        "//libspu/mpc:io_interface",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/common:pv2k",
        "//libspu/mpc/standard_shape:protocol",
        "@yacl//yacl/link",
    ],
)

spu_cc_test(
    name = "ref2k_test",
    srcs = ["ref2k_test.cc"],
    deps = [
        ":ref2k",
        "//libspu/mpc:api_test",
        "//libspu/mpc:io_test",
    ],
)
