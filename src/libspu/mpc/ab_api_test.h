// Copyright 2021 Ant Group Co., Ltd.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "gtest/gtest.h"
#include "yacl/link/link.h"

#include "libspu/mpc/api_test_params.h"

namespace spu::mpc::test {

class ArithmeticTest : public ::testing::TestWithParam<OpTestParams> {};

class BooleanTest : public ::testing::TestWithParam<OpTestParams> {};

class ConversionTest : public ::testing::TestWithParam<OpTestParams> {};

}  // namespace spu::mpc::test
