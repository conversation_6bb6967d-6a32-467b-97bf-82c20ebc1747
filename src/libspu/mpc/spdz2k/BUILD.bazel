# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "spdz2k",
    deps = [
        ":io",
        ":protocol",
    ],
)

spu_cc_library(
    name = "protocol",
    srcs = ["protocol.cc"],
    hdrs = ["protocol.h"],
    deps = [
        ":arithmetic",
        ":boolean",
        ":conversion",
        ":state",
        ":value",
        "//libspu/core:context",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/standard_shape:protocol",
    ],
)

spu_cc_test(
    name = "protocol_api_test",
    srcs = ["protocol_api_test.cc"],
    deps = [
        ":protocol",
        "//libspu/mpc:api_test",
    ],
)

spu_cc_test(
    name = "protocol_ab_test",
    timeout = "eternal",
    srcs = ["protocol_ab_test.cc"],
    deps = [
        ":abprotocol_spdz2k_test",
        ":protocol",
        "//libspu/mpc:ab_api_test",
    ],
)

spu_cc_library(
    name = "state",
    hdrs = ["state.h"],
    deps = [
        ":commitment",
        "//libspu/mpc/spdz2k/beaver:beaver_tfp",
        "//libspu/mpc/spdz2k/beaver:beaver_tinyot",
    ],
)

spu_cc_library(
    name = "arithmetic",
    srcs = ["arithmetic.cc"],
    hdrs = ["arithmetic.h"],
    deps = [
        ":state",
        ":type",
        ":value",
        "//libspu/core:vectorize",
        "//libspu/mpc:ab_api",
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/utils:circuits",
        "//libspu/mpc/utils:ring_ops",
        "@yacl//yacl/crypto/hash:blake3",
        "@yacl//yacl/crypto/hash:hash_utils",
    ],
)

spu_cc_library(
    name = "boolean",
    srcs = ["boolean.cc"],
    hdrs = ["boolean.h"],
    deps = [
        ":state",
        ":type",
        ":value",
        "//libspu/mpc:ab_api",
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/common:prg_state",
    ],
)

spu_cc_library(
    name = "conversion",
    srcs = ["conversion.cc"],
    hdrs = ["conversion.h"],
    deps = [
        ":arithmetic",
        ":boolean",
        ":state",
        ":type",
        "//libspu/core:vectorize",
        "//libspu/core:xt_helper",
        "//libspu/mpc:ab_api",
        "//libspu/mpc:api",
        "//libspu/mpc:kernel",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/utils:circuits",
    ],
)

spu_cc_library(
    name = "commitment",
    srcs = ["commitment.cc"],
    hdrs = ["commitment.h"],
    deps = [
        "//libspu/core:prelude",
        "@yacl//yacl/crypto/hash:blake3",
        "@yacl//yacl/crypto/hash:hash_utils",
        "@yacl//yacl/crypto/rand",
        "@yacl//yacl/link",
    ],
)

spu_cc_library(
    name = "io",
    srcs = ["io.cc"],
    hdrs = ["io.h"],
    deps = [
        ":type",
        ":value",
        "//libspu/mpc:io_interface",
    ],
)

spu_cc_test(
    name = "io_test",
    srcs = ["io_test.cc"],
    deps = [
        ":io",
        "//libspu/mpc:io_test",
    ],
)

spu_cc_library(
    name = "type",
    srcs = ["type.cc"],
    hdrs = ["type.h"],
    deps = [
        "//libspu/core:type",
        "//libspu/mpc/common:pv2k",
    ],
)

spu_cc_test(
    name = "type_test",
    srcs = ["type_test.cc"],
    deps = [
        ":type",
    ],
)

spu_cc_library(
    name = "value",
    srcs = ["value.cc"],
    hdrs = ["value.h"],
    deps = [
        ":type",
        "//libspu/core:ndarray_ref",
        "//libspu/mpc/common:pv2k",
        "//libspu/mpc/utils:ring_ops",
    ],
)

spu_cc_library(
    name = "abprotocol_spdz2k_test",
    testonly = 1,
    srcs = ["abprotocol_spdz2k_test.cc"],
    deps = [
        "//libspu/mpc:ab_api",
        "//libspu/mpc:ab_api_test",
        "//libspu/mpc:api",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/common:pv2k",
        "//libspu/mpc/utils:simulate",
        "@googletest//:gtest",
    ],
    alwayslink = True,
)
