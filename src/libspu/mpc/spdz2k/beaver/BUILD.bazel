# Copyright 2021 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

load("//bazel:spu.bzl", "spu_cc_library", "spu_cc_test")

package(default_visibility = ["//visibility:public"])

spu_cc_library(
    name = "beaver_interface",
    hdrs = ["beaver_interface.h"],
    deps = [
        "//libspu/core:ndarray_ref",
    ],
)

spu_cc_library(
    name = "beaver_tfp",
    srcs = ["beaver_tfp.cc"],
    hdrs = ["beaver_tfp.h"],
    deps = [
        ":beaver_interface",
        ":trusted_party",
        "//libspu/mpc/common:communicator",
        "//libspu/mpc/common:prg_tensor",
        "//libspu/mpc/spdz2k:commitment",
        "//libspu/mpc/utils:ring_ops",
        "@seal",
        "@yacl//yacl/crypto/block_cipher:symmetric_crypto",
        "@yacl//yacl/crypto/tools:prg",
        "@yacl//yacl/link",
        "@yacl//yacl/utils:parallel",
    ],
)

spu_cc_test(
    name = "beaver_test",
    timeout = "eternal",
    srcs = ["beaver_test.cc"],
    deps = [
        ":beaver_tfp",
        ":beaver_tinyot",
        "//libspu/mpc/utils:simulate",
        "@googletest//:gtest",
    ],
)

spu_cc_library(
    name = "trusted_party",
    srcs = ["trusted_party.cc"],
    hdrs = ["trusted_party.h"],
    deps = [
        "//libspu/core:type_util",
        "//libspu/mpc/common:prg_tensor",
        "//libspu/mpc/utils:ring_ops",
    ],
)

spu_cc_library(
    name = "beaver_tinyot",
    srcs = ["beaver_tinyot.cc"],
    hdrs = ["beaver_tinyot.h"],
    deps = [
        ":beaver_interface",
        ":trusted_party",
        "//libspu/mpc/common:prg_state",
        "//libspu/mpc/spdz2k:commitment",
        "//libspu/mpc/spdz2k/ot:basic_ot_prot",
        "//libspu/mpc/spdz2k/ot:kos_ote",
        "//libspu/mpc/spdz2k/ot:tiny_ot",
        "//libspu/mpc/utils:ring_ops",
        "@yacl//yacl/crypto/tools:prg",
        "@yacl//yacl/kernel/type:ot_store",
        "@yacl//yacl/link",
        "@yacl//yacl/utils:matrix_utils",
        "@yacl//yacl/utils:serialize",
    ],
)
