# Copyright 2024 Ant Group Co., Ltd.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

###############################################################################
# <PERSON><PERSON> now uses Bzlmod by default to manage external dependencies.
# Please consider migrating your external dependencies from WORKSPACE to MODULE.bazel.
#
# For more details, please check https://github.com/bazelbuild/bazel/issues/18958
###############################################################################

module(
    name = "spulib",
    version = "0.9.4.dev20250522",
    compatibility_level = 1,
)

bazel_dep(name = "grpc", version = "1.66.0.bcr.4")
bazel_dep(name = "protobuf", version = "27.3")
single_version_override(
    module_name = "protobuf",
    patch_strip = 1,
    patches = [
        "//bazel/patches:protobuf-xla.patch",
    ],
    version = "27.3",
)

bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "apple_support", version = "1.17.1")
bazel_dep(name = "rules_cc", version = "0.0.12")
bazel_dep(name = "rules_cuda", version = "0.2.3")
bazel_dep(name = "rules_foreign_cc", version = "0.12.0")
bazel_dep(name = "bazel_features", version = "1.20.0")
bazel_dep(name = "platforms", version = "0.0.8")
bazel_dep(name = "rules_proto", version = "6.0.0-rc1")
bazel_dep(name = "spdlog", version = "1.14.1")
bazel_dep(name = "fmt", version = "11.0.2")
bazel_dep(name = "abseil-cpp", version = "20240722.0")
bazel_dep(name = "rules_python", version = "0.29.0")
bazel_dep(name = "magic_enum", version = "0.9.6")

python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    configure_coverage_tool = False,
    ignore_root_user_error = True,
    python_version = "3.11",
)

# --registry=https://baidu.github.io/babylon/registry
bazel_dep(name = "leveldb", version = "1.23")

# self-hosted registry
bazel_dep(name = "eigen", version = "3.4.90-20230801-66e8f3")
bazel_dep(name = "emp-tool", version = "0.2.5.bcr.1")
bazel_dep(name = "emp-ot", version = "0.2.4.bcr.1")
bazel_dep(name = "brpc", version = "1.11.0-20241212-282bc90")
bazel_dep(name = "seal", version = "4.1.1")
bazel_dep(name = "cutlass", version = "3.5.1")
bazel_dep(name = "llvm-raw", version = "20240809.0-35f55f5")
bazel_dep(name = "sse2neon", version = "1.7.0-20240330-8df2f48")

llvm = use_extension("@llvm-raw//utils/bazel:extension.bzl", "llvm")
llvm.configure(
    targets = [
        "AArch64",
        "X86",
        "ARM",
    ],
)
use_repo(llvm, "llvm-project")

bazel_dep(name = "stablehlo", version = "20240808.0-24d1807")
bazel_dep(name = "xla", version = "20240814.0-64bdcc5")
bazel_dep(name = "yacl", version = "0.4.5b10-nightly-20250110")

spu_dependencies = use_extension("//bazel:defs.bzl", "non_module_dependencies")
use_repo(spu_dependencies, "xtensor")

new_local_repository = use_repo_rule("@bazel_tools//tools/build_defs/repo:local.bzl", "new_local_repository")

new_local_repository(
    name = "macos_omp_x64",
    build_file = "@yacl//bazel:local_openmp_macos.BUILD",
    path = "/usr/local/opt/libomp",
)

new_local_repository(
    name = "macos_omp_arm64",
    build_file = "@yacl//bazel:local_openmp_macos.BUILD",
    path = "/opt/homebrew/opt/libomp/",
)

# test
bazel_dep(name = "googletest", version = "1.15.2", dev_dependency = True)
bazel_dep(name = "google_benchmark", version = "1.8.5", dev_dependency = True)
